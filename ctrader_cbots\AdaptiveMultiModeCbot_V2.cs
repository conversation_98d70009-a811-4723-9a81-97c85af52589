using System;
using System.Linq;
using cAlgo.API;
using cAlgo.API.Indicators;
using cAlgo.API.Internals;
using cAlgo.Indicators;

namespace cAlgo.Robots
{
    [Robot(TimeZone = TimeZones.UTC, AccessRights = AccessRights.None)]
    public class AdaptiveMultiModeCbot_V2 : Robot
    {
        #region Parameters
        [Parameter("Risk Per Trade (%)", DefaultValue = 1.0, MinValue = 0.1, MaxValue = 5.0)]
        public double RiskPerTrade { get; set; }

        [Parameter("Max Daily Risk (%)", DefaultValue = 3.0, MinValue = 1.0, MaxValue = 10.0)]
        public double MaxDailyRisk { get; set; }

        [Parameter("ATR Period", DefaultValue = 14, MinValue = 5, MaxValue = 50)]
        public int AtrPeriod { get; set; }

        [Parameter("ATR SL Multiplier", DefaultValue = 2.0, MinValue = 1.0, MaxValue = 5.0)]
        public double AtrSlMultiplier { get; set; }

        [Parameter("ADX Period", DefaultValue = 14, MinValue = 5, MaxValue = 50)]
        public int AdxPeriod { get; set; }

        [Parameter("ADX Threshold", DefaultValue = 30, MinValue = 20, MaxValue = 50)]
        public double AdxThreshold { get; set; }

        [Parameter("BB Period", DefaultValue = 20, MinValue = 10, MaxValue = 50)]
        public int BbPeriod { get; set; }

        [Parameter("BB StdDev", DefaultValue = 2.0, MinValue = 1.0, MaxValue = 3.0)]
        public double BbStdDev { get; set; }

        [Parameter("RSI Period", DefaultValue = 14, MinValue = 5, MaxValue = 50)]
        public int RsiPeriod { get; set; }

        [Parameter("Volume MA Period", DefaultValue = 20, MinValue = 5, MaxValue = 50)]
        public int VolumeMaPeriod { get; set; }

        [Parameter("Max Spread (Pips)", DefaultValue = 4.0, MinValue = 1.0, MaxValue = 10.0)]
        public double MaxSpreadPips { get; set; }

        [Parameter("Enable Trading Session Filter", DefaultValue = true)]
        public bool EnableSessionFilter { get; set; }

        [Parameter("Enable Volume Filter", DefaultValue = true)]
        public bool EnableVolumeFilter { get; set; }

        [Parameter("Enable Multi-Timeframe", DefaultValue = true)]
        public bool EnableMultiTimeframe { get; set; }
        #endregion

        #region Private Fields
        private AverageTrueRange _atr;
        private DirectionalMovementSystem _adx;
        private BollingerBands _bb;
        private RelativeStrengthIndex _rsi;
        private MovingAverage _volumeMa;
        private MovingAverage _ema200H1;
        
        private Bars _h1Bars;
        private AverageTrueRange _atrH1;
        private DirectionalMovementSystem _adxH1;
        
        private double _dailyRisk = 0;
        private DateTime _lastTradeDate = DateTime.MinValue;
        private const string LABEL = "AdaptiveV2";
        #endregion

        #region Initialization
        protected override void OnStart()
        {
            Print($"AdaptiveMultiModeCbot V2 启动 - 时间框架: {TimeFrame}");
            
            // 初始化指标
            _atr = Indicators.AverageTrueRange(AtrPeriod);
            _adx = Indicators.DirectionalMovementSystem(AdxPeriod);
            _bb = Indicators.BollingerBands(Bars.ClosePrices, BbPeriod, BbStdDev, MovingAverageType.Simple);
            _rsi = Indicators.RelativeStrengthIndex(Bars.ClosePrices, RsiPeriod);
            _volumeMa = Indicators.MovingAverage(Bars.TickVolumes, VolumeMaPeriod, MovingAverageType.Simple);
            
            // 多时间框架分析
            if (EnableMultiTimeframe && TimeFrame != TimeFrame.Hour)
            {
                _h1Bars = MarketData.GetBars(TimeFrame.Hour);
                _atrH1 = Indicators.AverageTrueRange(_h1Bars, AtrPeriod);
                _adxH1 = Indicators.DirectionalMovementSystem(_h1Bars, AdxPeriod);
                _ema200H1 = Indicators.MovingAverage(_h1Bars.ClosePrices, 200, MovingAverageType.Exponential);
            }
            
            Print("所有指标初始化完成");
        }
        #endregion

        #region Main Trading Logic
        protected override void OnBar()
        {
            // 基础检查
            if (!IsValidTradingConditions())
                return;

            // 重置每日风险
            ResetDailyRiskIfNeeded();

            // 检查现有持仓
            var existingPosition = Positions.Find(LABEL);
            if (existingPosition != null)
            {
                ManageExistingPosition(existingPosition);
                return;
            }

            // 生成交易信号
            var signalResult = GenerateTradeSignal();
            if (!signalResult.HasValue)
                return;

            // 执行交易
            ExecuteTrade(signalResult.Value);
        }
        #endregion

        #region Trading Conditions
        private bool IsValidTradingConditions()
        {
            // 检查数据可用性
            if (Bars.Count < Math.Max(BbPeriod, Math.Max(AdxPeriod, AtrPeriod)) + 1)
                return false;

            // 检查点差
            if (Symbol.Spread / Symbol.PipSize > MaxSpreadPips)
            {
                Print($"点差过大: {Symbol.Spread / Symbol.PipSize:F1} pips > {MaxSpreadPips}");
                return false;
            }

            // 检查交易时段
            if (EnableSessionFilter && !IsActiveSession())
                return false;

            // 检查每日风险限制
            if (_dailyRisk >= MaxDailyRisk)
            {
                Print($"已达到每日最大风险限制: {_dailyRisk:F1}%");
                return false;
            }

            return true;
        }

        private bool IsActiveSession()
        {
            var utcTime = Server.Time;
            var hour = utcTime.Hour;
            
            // 伦敦会话: 07:00-16:00 UTC
            // 纽约会话: 13:00-22:00 UTC
            // 最佳时段: 13:00-16:00 UTC (伦敦纽约重叠)
            
            bool isLondonSession = hour >= 7 && hour < 16;
            bool isNewYorkSession = hour >= 13 && hour < 22;
            bool isPrimeTime = hour >= 13 && hour < 16; // 重叠时段
            
            // 优先重叠时段，其次是主要会话
            return isPrimeTime || isLondonSession || isNewYorkSession;
        }
        #endregion

        #region Signal Generation
        private TradeType? GenerateTradeSignal()
        {
            var currentIndex = Bars.Count - 1;
            var prevIndex = currentIndex - 1;

            // 获取指标值
            var adxValue = _adx.ADX[currentIndex];
            var diPlus = _adx.DIPlus[currentIndex];
            var diMinus = _adx.DIMinus[currentIndex];
            
            var bbUpper = _bb.Top[currentIndex];
            var bbLower = _bb.Bottom[currentIndex];
            var bbMiddle = _bb.Main[currentIndex];
            
            var rsiValue = _rsi.Result[currentIndex];
            var close = Bars.ClosePrices[currentIndex];
            var prevClose = Bars.ClosePrices[prevIndex];
            
            // 多时间框架过滤
            if (EnableMultiTimeframe && !IsMultiTimeframeBullish() && !IsMultiTimeframeBearish())
                return null;

            // 成交量确认
            if (EnableVolumeFilter && !IsVolumeConfirmed())
                return null;

            // 判断市场状态
            bool isTrendingMarket = adxValue > AdxThreshold;
            
            if (isTrendingMarket)
            {
                return GenerateTrendSignal(diPlus, diMinus, close, bbUpper, bbLower, rsiValue);
            }
            else
            {
                return GenerateRangeSignal(close, bbUpper, bbLower, bbMiddle, rsiValue);
            }
        }

        private TradeType? GenerateTrendSignal(double diPlus, double diMinus, double close, double bbUpper, double bbLower, double rsiValue)
        {
            bool bullishTrend = diPlus > diMinus;
            bool bearishTrend = diMinus > diPlus;
            
            // 趋势突破信号 + RSI 过滤
            if (bullishTrend && close > bbUpper && rsiValue < 70) // 避免超买区域
            {
                Print($"趋势做多信号: DI+({diPlus:F1}) > DI-({diMinus:F1}), 价格突破上轨, RSI={rsiValue:F1}");
                return TradeType.Buy;
            }
            
            if (bearishTrend && close < bbLower && rsiValue > 30) // 避免超卖区域
            {
                Print($"趋势做空信号: DI-({diMinus:F1}) > DI+({diPlus:F1}), 价格突破下轨, RSI={rsiValue:F1}");
                return TradeType.Sell;
            }
            
            return null;
        }

        private TradeType? GenerateRangeSignal(double close, double bbUpper, double bbLower, double bbMiddle, double rsiValue)
        {
            // 震荡区间均值回归策略
            double upperThreshold = bbMiddle + (bbUpper - bbMiddle) * 0.8; // 80% 位置
            double lowerThreshold = bbMiddle - (bbMiddle - bbLower) * 0.8;
            
            // 从上轨回归中线
            if (close < upperThreshold && close > bbMiddle && rsiValue > 60)
            {
                Print($"区间做空信号: 价格从上轨回归, RSI={rsiValue:F1}");
                return TradeType.Sell;
            }
            
            // 从下轨回归中线
            if (close > lowerThreshold && close < bbMiddle && rsiValue < 40)
            {
                Print($"区间做多信号: 价格从下轨回归, RSI={rsiValue:F1}");
                return TradeType.Buy;
            }
            
            return null;
        }

        private bool IsMultiTimeframeBullish()
        {
            if (!EnableMultiTimeframe || _h1Bars == null)
                return true;
                
            var h1Index = _h1Bars.Count - 1;
            var h1Close = _h1Bars.ClosePrices[h1Index];
            var ema200Value = _ema200H1.Result[h1Index];
            var h1Adx = _adxH1.ADX[h1Index];
            
            return h1Close > ema200Value && h1Adx > 25;
        }

        private bool IsMultiTimeframeBearish()
        {
            if (!EnableMultiTimeframe || _h1Bars == null)
                return true;
                
            var h1Index = _h1Bars.Count - 1;
            var h1Close = _h1Bars.ClosePrices[h1Index];
            var ema200Value = _ema200H1.Result[h1Index];
            var h1Adx = _adxH1.ADX[h1Index];
            
            return h1Close < ema200Value && h1Adx > 25;
        }

        private bool IsVolumeConfirmed()
        {
            if (!EnableVolumeFilter)
                return true;
                
            var currentIndex = Bars.Count - 1;
            var currentVolume = Bars.TickVolumes[currentIndex];
            var avgVolume = _volumeMa.Result[currentIndex];
            
            // 要求当前成交量至少是平均值的 1.2 倍
            return currentVolume > avgVolume * 1.2;
        }
        #endregion

        #region Trade Execution
        private void ExecuteTrade(TradeType tradeType)
        {
            var currentIndex = Bars.Count - 1;
            var atrValue = _atr.Result[currentIndex];
            var close = Bars.ClosePrices[currentIndex];
            
            // 计算仓位大小（固定风险金额）
            var riskAmount = Account.Balance * (RiskPerTrade / 100.0);
            var stopLossPips = atrValue / Symbol.PipSize * AtrSlMultiplier;
            var volumeInUnits = CalculateVolumeInUnits(riskAmount, stopLossPips);
            
            if (volumeInUnits < Symbol.VolumeInUnitsMin)
            {
                Print($"计算的手数过小: {volumeInUnits} < {Symbol.VolumeInUnitsMin}");
                return;
            }

            // 计算止损止盈
            double? stopLoss = null;
            double? takeProfit = null;
            
            if (tradeType == TradeType.Buy)
            {
                stopLoss = close - (atrValue * AtrSlMultiplier);
                takeProfit = close + (atrValue * AtrSlMultiplier * 1.5); // 1:1.5 风险收益比
            }
            else
            {
                stopLoss = close + (atrValue * AtrSlMultiplier);
                takeProfit = close - (atrValue * AtrSlMultiplier * 1.5);
            }

            var result = ExecuteMarketOrder(tradeType, SymbolName, volumeInUnits, LABEL, stopLoss, takeProfit);
            
            if (result.IsSuccessful)
            {
                _dailyRisk += RiskPerTrade;
                _lastTradeDate = Server.Time.Date;
                
                var tradeTypeStr = tradeType == TradeType.Buy ? "买入" : "卖出";
                Print($"交易执行成功: {tradeTypeStr} {volumeInUnits} 手, SL: {stopLoss:F2}, TP: {takeProfit:F2}");
                Print($"当日风险累计: {_dailyRisk:F1}%");
            }
            else
            {
                Print($"交易执行失败: {result.Error}");
            }
        }

        private long CalculateVolumeInUnits(double riskAmount, double stopLossPips)
        {
            var pipValue = Symbol.PipValue;
            var volumeInUnits = riskAmount / (stopLossPips * pipValue);
            
            // 调整到符合交易商要求的手数
            var normalizedVolume = Symbol.NormalizeVolumeInUnits((long)volumeInUnits, RoundingMode.Down);
            
            return Math.Max(normalizedVolume, Symbol.VolumeInUnitsMin);
        }
        #endregion

        #region Position Management
        private void ManageExistingPosition(Position position)
        {
            // 简单的追踪止损逻辑
            var currentIndex = Bars.Count - 1;
            var atrValue = _atr.Result[currentIndex];
            var close = Bars.ClosePrices[currentIndex];
            
            if (position.TradeType == TradeType.Buy)
            {
                var newStopLoss = close - (atrValue * AtrSlMultiplier);
                if (position.StopLoss.HasValue && newStopLoss > position.StopLoss.Value)
                {
                    var result = ModifyPosition(position, newStopLoss, position.TakeProfit);
                    if (result.IsSuccessful)
                        Print($"追踪止损更新: {newStopLoss:F2}");
                }
            }
            else
            {
                var newStopLoss = close + (atrValue * AtrSlMultiplier);
                if (position.StopLoss.HasValue && newStopLoss < position.StopLoss.Value)
                {
                    var result = ModifyPosition(position, newStopLoss, position.TakeProfit);
                    if (result.IsSuccessful)
                        Print($"追踪止损更新: {newStopLoss:F2}");
                }
            }
        }
        #endregion

        #region Utility Methods
        private void ResetDailyRiskIfNeeded()
        {
            if (Server.Time.Date != _lastTradeDate)
            {
                _dailyRisk = 0;
                Print($"新交易日开始，重置每日风险计数");
            }
        }

        protected override void OnStop()
        {
            Print("AdaptiveMultiModeCbot V2 停止");
        }
        #endregion
    }
} 