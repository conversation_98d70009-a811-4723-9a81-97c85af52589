using System;
using cAlgo.API;
using cAlgo.API.Indicators;
using cAlgo.API.Internals;

namespace cAlgo
{
    [Robot(TimeZone = TimeZones.UTC, AccessRights = AccessRights.None)]
    public class DualModeCbot : Robot
    {
        // === 参数 ===
        [Parameter("开仓手数(标准手)", DefaultValue = 0.01, MinValue = 0.01)]
        public double VolumeInLots { get; set; }

        [Parameter("ATR 周期", DefaultValue = 14)]
        public int AtrPeriod { get; set; }

        [Parameter("BB 周期", DefaultValue = 20)]
        public int BbPeriod { get; set; }

        [Parameter("BB 偏差", DefaultValue = 2.5)]
        public double BbStd { get; set; }

        [Parameter("Regime 阀值", DefaultValue = 0.6)]
        public double RegimeThreshold { get; set; }

        [Parameter("止损 ATR 倍数", DefaultValue = 1.2)]
        public double SlAtrMultiplier { get; set; }

        [Parameter("止盈倍数(风险)", DefaultValue = 2.0)]
        public double TpRiskReward { get; set; }

        // === 内部字段 ===
        private AverageTrueRange _atr15;
        private AverageTrueRange _atr30;
        private AverageTrueRange _atrDaily;
        private BollingerBands _bb;

        private Bars _tradeBars;
        private Bars _bars30;
        private Bars _barsDaily;

        private enum Regime { Trend, Reversion }

        protected override void OnStart()
        {
            _tradeBars = Bars;
            _bars30 = MarketData.GetBars(TimeFrame.Minute30);
            _barsDaily = MarketData.GetBars(TimeFrame.Daily);

            _atr15 = Indicators.AverageTrueRange(_tradeBars, AtrPeriod, MovingAverageType.Simple);
            _atr30 = Indicators.AverageTrueRange(_bars30, AtrPeriod, MovingAverageType.Simple);
            _atrDaily = Indicators.AverageTrueRange(_barsDaily, 20, MovingAverageType.Simple); // 20 日

            _bb = Indicators.BollingerBands(_tradeBars.ClosePrices, BbPeriod, BbStd, MovingAverageType.Simple);
        }

        protected override void OnBar()
        {
            if (!IsTradingSession())
                return;

            var regime = GetCurrentRegime();

            if (regime == Regime.Trend)
                ExecuteTrendLogic();
            else
                ExecuteReversionLogic();
        }

        private Regime GetCurrentRegime()
        {
            // 计算波动率比值 V = ATR30 / ATR20Daily
            double atr30Val = _atr30.Result.LastValue;
            double atrDailyVal = _atrDaily.Result.LastValue;

            double v = atrDailyVal > 0 ? atr30Val / atrDailyVal : 0;
            return v > RegimeThreshold ? Regime.Trend : Regime.Reversion;
        }

        private bool IsTradingSession()
        {
            // 过滤掉点差过大时段
            double spreadPips = Symbol.Spread / Symbol.PipSize;
            if (Symbol.Name.Contains("XAU") && spreadPips > 4)
                return false;
            if (Symbol.Name.Contains("US500") && spreadPips > 2)
                return false;
            return true;
        }

        private void ExecuteTrendLogic()
        {
            // 已有仓位则管理，否则寻找突破机会
            if (Positions.Count > 0)
            {
                ManagePositions();
                return;
            }

            double upper = _bb.Top.LastValue;
            double lower = _bb.Bottom.LastValue;
            double lastClose = _tradeBars.ClosePrices.LastValue;
            double atr = _atr15.Result.LastValue;

            double volume = Symbol.QuantityToVolumeInUnits(VolumeInLots);
            var slPips = atr * SlAtrMultiplier / Symbol.PipSize;
            double tpPips = slPips * TpRiskReward;

            if (lastClose > upper)
            {
                PlaceTrade(TradeType.Buy, volume, slPips, tpPips, "TrendBreakoutBuy");
            }
            else if (lastClose < lower)
            {
                PlaceTrade(TradeType.Sell, volume, slPips, tpPips, "TrendBreakoutSell");
            }
        }

        private void ExecuteReversionLogic()
        {
            // 均值回归: 上轨卖, 下轨买, 中轨平仓
            double upper = _bb.Top.LastValue;
            double lower = _bb.Bottom.LastValue;
            double middle = _bb.Main.LastValue;
            double lastClose = _tradeBars.ClosePrices.LastValue;
            double atr = _atr15.Result.LastValue;
            double slPips = atr * SlAtrMultiplier / Symbol.PipSize;
            double volume = Symbol.QuantityToVolumeInUnits(VolumeInLots);

            foreach (var pos in Positions)
            {
                // 如果价格回到中线, 平仓
                if ((pos.TradeType == TradeType.Buy && lastClose >= middle) ||
                    (pos.TradeType == TradeType.Sell && lastClose <= middle))
                {
                    ClosePosition(pos);
                }
            }

            // 如果仍无仓位, 尝试开仓
            if (Positions.Count == 0)
            {
                if (lastClose > upper)
                    PlaceTrade(TradeType.Sell, volume, slPips, slPips, "ReversionSell");
                else if (lastClose < lower)
                    PlaceTrade(TradeType.Buy, volume, slPips, slPips, "ReversionBuy");
            }
        }

        private void ManagePositions()
        {
            double lastClose = _tradeBars.ClosePrices.LastValue;
            double upper = _bb.Top.LastValue;
            double lower = _bb.Bottom.LastValue;
            double middle = _bb.Main.LastValue;

            foreach (var pos in Positions)
            {
                // 趋势模式: 当价格回到中轨或达到 TP, 系统会自动平仓; 这里额外设置移动止损实现保本
                if (pos.TradeType == TradeType.Buy)
                {
                    double newSl = pos.EntryPrice + (Symbol.PipSize * 10); // 保本 +1 pip
                    if (newSl > pos.StopLoss) 
                        ModifyPosition(pos, newSl, pos.TakeProfit);

                    if (lastClose < middle)
                        ClosePosition(pos);
                }
                else if (pos.TradeType == TradeType.Sell)
                {
                    double newSl = pos.EntryPrice - (Symbol.PipSize * 10);
                    if (newSl < pos.StopLoss)
                        ModifyPosition(pos, newSl, pos.TakeProfit);

                    if (lastClose > middle)
                        ClosePosition(pos);
                }
            }
        }

        private void PlaceTrade(TradeType tradeType, double volume, double slPips, double tpPips, string label)
        {
            double slPrice = tradeType == TradeType.Buy ? Symbol.Bid - slPips * Symbol.PipSize : Symbol.Ask + slPips * Symbol.PipSize;
            double tpPrice = tradeType == TradeType.Buy ? Symbol.Bid + tpPips * Symbol.PipSize : Symbol.Ask - tpPips * Symbol.PipSize;

            ExecuteMarketOrder(tradeType, Symbol.Name, volume, label, slPips, tpPips);
        }
    }
} 