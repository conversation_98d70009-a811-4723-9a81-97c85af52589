# Hummingbot策略快速索引

## 🚀 新手推荐策略

### 1. 基础做市策略
**文件位置**: `scripts/simple_pmm.py`
**适用场景**: 初学者学习做市机制
**特点**: 简单易懂，风险相对较低

### 2. 基础VWAP策略  
**文件位置**: `scripts/simple_vwap.py`
**适用场景**: 大单分拆执行
**特点**: 减少市场冲击，稳定执行

### 3. 基础跨交易所做市
**文件位置**: `scripts/simple_xemm.py`
**适用场景**: 跨交易所价差套利
**特点**: 利用价差获利，需要多个交易所

## 💼 专业交易策略

### 做市策略 (Market Making)
| 策略名称 | 文件位置 | 复杂度 | 特点 |
|---------|---------|--------|------|
| Pure Market Making | `v1_strategies/pure_market_making/` | ⭐⭐ | 经典做市，功能完整 |
| Avellaneda Market Making | `v1_strategies/avellaneda_market_making/` | ⭐⭐⭐⭐ | 学术级高级做市 |
| PMM Dynamic | `controllers/market_making/pmm_dynamic.py` | ⭐⭐⭐ | 动态参数调整 |

### 套利策略 (Arbitrage)
| 策略名称 | 文件位置 | 复杂度 | 特点 |
|---------|---------|--------|------|
| Cross-Exchange Market Making | `v1_strategies/cross_exchange_market_making/` | ⭐⭐⭐ | 跨交易所做市套利 |
| AMM Arbitrage | `v1_strategies/amm_arb/` | ⭐⭐⭐ | AMM与订单簿套利 |
| Spot Perpetual Arbitrage | `v1_strategies/spot_perpetual_arbitrage/` | ⭐⭐⭐⭐ | 现货永续套利 |
| Triangular Arbitrage | `scripts/community/triangular_arbitrage.py` | ⭐⭐⭐⭐ | 三角套利 |

### 方向性策略 (Directional)
| 策略名称 | 文件位置 | 复杂度 | 特点 |
|---------|---------|--------|------|
| RSI方向性策略 | `scripts/community/directional_strategy_rsi_spot.py` | ⭐⭐⭐ | 基于RSI指标 |
| MACD布林带策略 | `scripts/community/macd_bb_directional_strategy.py` | ⭐⭐⭐ | 多指标组合 |
| 布林带控制器 | `controllers/directional_trading/bollinger_v1.py` | ⭐⭐⭐ | V2框架布林带 |
| SuperTrend控制器 | `controllers/directional_trading/supertrend_v1.py` | ⭐⭐⭐ | 趋势跟随策略 |

## 🔧 V2框架高级策略

### 执行器组件
| 组件名称 | 文件位置 | 用途 |
|---------|---------|------|
| Position Executor | `v2_strategies/executors/position_executor/` | 仓位管理 |
| DCA Executor | `v2_strategies/executors/dca_executor/` | 定投策略 |
| Grid Executor | `v2_strategies/executors/grid_executor/` | 网格交易 |
| TWAP Executor | `v2_strategies/executors/twap_executor/` | 时间加权平均价格 |

### V2策略示例
| 策略名称 | 文件位置 | 特点 |
|---------|---------|------|
| V2方向性RSI | `scripts/v2_directional_rsi.py` | 现代框架RSI策略 |
| V2资金费率套利 | `scripts/v2_funding_rate_arb.py` | 永续合约资金费率套利 |
| V2多对TWAP | `scripts/v2_twap_multiple_pairs.py` | 多交易对同时执行 |

## 🛠️ 实用工具策略

### 数据和监控
| 工具名称 | 文件位置 | 用途 |
|---------|---------|------|
| 下载K线数据 | `scripts/utility/download_candles.py` | 获取历史数据 |
| 下载订单簿数据 | `scripts/download_order_book_and_trades.py` | 市场数据收集 |
| 清算监控 | `scripts/utility/liquidations_example.py` | 监控清算事件 |

### 回测工具
| 工具名称 | 文件位置 | 用途 |
|---------|---------|------|
| 做市策略回测 | `scripts/utility/backtest_mm_example.py` | 做市策略回测 |
| DCA示例 | `scripts/utility/dca_example.py` | 定投策略示例 |

## 📚 社区贡献策略

### 热门社区策略
| 策略名称 | 文件位置 | 特点 |
|---------|---------|------|
| 固定网格 | `scripts/community/fixed_grid.py` | 简单网格交易 |
| 买低卖高 | `scripts/community/buy_low_sell_high.py` | 低买高卖策略 |
| 1/N投资组合 | `scripts/community/1overN_portfolio.py` | 等权重投资组合 |
| 买入下跌 | `scripts/community/buy_dip_example.py` | 逢低买入策略 |

## 🎯 按交易所类型选择

### 中心化交易所(CEX)策略
- Pure Market Making - 适用于所有CEX
- Cross-Exchange Market Making - 需要多个CEX
- Perpetual Market Making - 适用于期货交易所

### 去中心化交易所(DEX)策略  
- AMM Arbitrage - 适用于Uniswap等AMM
- AMM价格示例 - `scripts/amm_price_example.py`
- AMM交易示例 - `scripts/amm_trade_example.py`

## ⚡ 快速启动指南

### 1. 新手入门路径
```
scripts/basic/ → scripts/simple_pmm.py → v1_strategies/pure_market_making/
```

### 2. 进阶学习路径
```
scripts/community/ → controllers/ → v2_strategies/
```

### 3. 专业交易者路径
```
v1_strategies/avellaneda_market_making/ → v2框架组合策略
```

## ⚠️ 重要提醒

1. **风险等级说明**:
   - ⭐ = 低风险/简单
   - ⭐⭐ = 中等风险
   - ⭐⭐⭐ = 较高风险  
   - ⭐⭐⭐⭐ = 高风险/复杂

2. **使用建议**:
   - 新手从基础示例开始
   - 充分了解策略原理再使用
   - 先在测试环境验证
   - 设置合理的风险参数

3. **必要准备**:
   - 安装Hummingbot环境
   - 准备交易所API密钥
   - 了解相关交易规则
   - 准备足够的资金进行测试 