using System;
using System.Linq;
using cAlgo.API;
using cAlgo.API.Indicators;

namespace cAlgo.Robots
{
    [Robot(TimeZone = TimeZones.UTC, AccessRights = AccessRights.None)]
    public class AdaptiveMultiModeCbot_V2_Fixed : Robot
    {
        #region Parameters
        [Parameter("Risk Per Trade (%)", DefaultValue = 1.0, MinValue = 0.1, MaxValue = 5.0)]
        public double RiskPerTrade { get; set; }

        [Parameter("Max Daily Risk (%)", DefaultValue = 3.0, MinValue = 1.0, MaxValue = 10.0)]
        public double MaxDailyRisk { get; set; }

        [Parameter("ATR Period", DefaultValue = 14, MinValue = 5, MaxValue = 50)]
        public int AtrPeriod { get; set; }

        [Parameter("ATR SL Multiplier", DefaultValue = 2.0, MinValue = 1.0, MaxValue = 5.0)]
        public double AtrSlMultiplier { get; set; }

        [Parameter("ADX Period", DefaultValue = 14, MinValue = 5, MaxValue = 50)]
        public int AdxPeriod { get; set; }

        [Parameter("ADX Threshold", DefaultValue = 30, MinValue = 20, MaxValue = 50)]
        public double AdxThreshold { get; set; }

        [Parameter("BB Period", DefaultValue = 20, MinValue = 10, MaxValue = 50)]
        public int BbPeriod { get; set; }

        [Parameter("BB StdDev", DefaultValue = 2.0, MinValue = 1.0, MaxValue = 3.0)]
        public double BbStdDev { get; set; }

        [Parameter("RSI Period", DefaultValue = 14, MinValue = 5, MaxValue = 50)]
        public int RsiPeriod { get; set; }

        [Parameter("Volume MA Period", DefaultValue = 20, MinValue = 5, MaxValue = 50)]
        public int VolumeMaPeriod { get; set; }

        [Parameter("Max Spread (Pips)", DefaultValue = 4.0, MinValue = 1.0, MaxValue = 10.0)]
        public double MaxSpreadPips { get; set; }

        [Parameter("Enable Trading Session Filter", DefaultValue = true)]
        public bool EnableSessionFilter { get; set; }

        [Parameter("Enable Volume Filter", DefaultValue = true)]
        public bool EnableVolumeFilter { get; set; }

        [Parameter("Enable Trend Filter", DefaultValue = true)]
        public bool EnableTrendFilter { get; set; }
        
        [Parameter("Trend MA Period", DefaultValue = 200, MinValue = 50, MaxValue = 500)]
        public int TrendMaPeriod { get; set; }

        [Parameter("Enable Kelly Criterion", DefaultValue = false, Group = "Position Sizing")]
        public bool EnableKellyCriterion { get; set; }

        [Parameter("Kelly Win Rate", DefaultValue = 0.45, MinValue = 0.01, MaxValue = 1.0, Group = "Position Sizing")]
        public double KellyWinRate { get; set; }

        [Parameter("Kelly Risk/Reward Ratio", DefaultValue = 1.5, MinValue = 0.1, MaxValue = 10.0, Group = "Position Sizing")]
        public double KellyRiskRewardRatio { get; set; }

        [Parameter("Kelly Safety Factor", DefaultValue = 0.5, MinValue = 0.1, MaxValue = 1.0, Group = "Position Sizing")]
        public double KellySafetyFactor { get; set; }
        #endregion

        #region Private Fields
        private AverageTrueRange _atr;
        private DirectionalMovementSystem _adx;
        private BollingerBands _bb;
        private RelativeStrengthIndex _rsi;
        private MovingAverage _trendMa;
        
        private double _dailyRisk = 0;
        private DateTime _lastTradeDate = DateTime.MinValue;
        private const string LABEL = "AdaptiveV2";
        #endregion

        #region Initialization
        protected override void OnStart()
        {
            Print($"AdaptiveMultiModeCbot V2 Fixed 启动 - 时间框架: {TimeFrame}");
            
            // 初始化指标
            _atr = Indicators.AverageTrueRange(AtrPeriod, MovingAverageType.Exponential);
            _adx = Indicators.DirectionalMovementSystem(AdxPeriod);
            _bb = Indicators.BollingerBands(Bars.ClosePrices, BbPeriod, BbStdDev, MovingAverageType.Simple);
            _rsi = Indicators.RelativeStrengthIndex(Bars.ClosePrices, RsiPeriod);
            _trendMa = Indicators.MovingAverage(Bars.ClosePrices, TrendMaPeriod, MovingAverageType.Exponential);
            // 暂时移除成交量指标，因为类型转换复杂
            // _volumeMa = Indicators.MovingAverage(Bars.TickVolumes, VolumeMaPeriod, MovingAverageType.Simple);
            
            Print("所有指标初始化完成");
        }
        #endregion

        #region Main Trading Logic
        protected override void OnBar()
        {
            // 基础检查
            if (!IsValidTradingConditions())
                return;

            // 重置每日风险
            ResetDailyRiskIfNeeded();

            // 检查现有持仓
            var existingPosition = Positions.Find(LABEL);
            if (existingPosition != null)
            {
                ManageExistingPosition(existingPosition);
                return;
            }

            // 生成交易信号
            var tradeSignal = GenerateTradeSignal();

            // 根据信号执行交易，并应用趋势过滤器
            if (tradeSignal == TradeType.Buy)
            {
                if (EnableTrendFilter && Bars.ClosePrices.LastValue < _trendMa.Result.LastValue)
                {
                    // Print("趋势过滤器阻止做多 (价格低于长期均线)");
                    return;
                }
                ExecuteTrade(TradeType.Buy);
            }
            else if (tradeSignal == TradeType.Sell)
            {
                if (EnableTrendFilter && Bars.ClosePrices.LastValue > _trendMa.Result.LastValue)
                {
                    // Print("趋势过滤器阻止做空 (价格高于长期均线)");
                    return;
                }
                ExecuteTrade(TradeType.Sell);
            }
        }
        #endregion

        #region Trading Conditions
        private bool IsValidTradingConditions()
        {
            // 检查数据可用性
            if (Bars.Count < Math.Max(BbPeriod, Math.Max(AdxPeriod, AtrPeriod)) + 1)
                return false;

            // 检查点差
            if (Symbol.Spread / Symbol.PipSize > MaxSpreadPips)
            {
                Print($"点差过大: {Symbol.Spread / Symbol.PipSize:F1} pips > {MaxSpreadPips}");
                return false;
            }

            // 检查交易时段
            if (EnableSessionFilter && !IsActiveSession())
                return false;

            // 检查每日风险限制
            if (_dailyRisk >= MaxDailyRisk)
            {
                Print($"已达到每日最大风险限制: {_dailyRisk:F1}%");
                return false;
            }

            return true;
        }

        private bool IsActiveSession()
        {
            var utcTime = Server.Time;
            var hour = utcTime.Hour;
            
            // 伦敦会话: 07:00-16:00 UTC
            // 纽约会话: 13:00-22:00 UTC
            // 最佳时段: 13:00-16:00 UTC (伦敦纽约重叠)
            
            bool isLondonSession = hour >= 7 && hour < 16;
            bool isNewYorkSession = hour >= 13 && hour < 22;
            bool isPrimeTime = hour >= 13 && hour < 16; // 重叠时段
            
            // 优先重叠时段，其次是主要会话
            return isPrimeTime || isLondonSession || isNewYorkSession;
        }
        #endregion

        #region Signal Generation
        private TradeType GenerateTradeSignal()
        {
            var currentIndex = Bars.Count - 1;

            // 获取指标值
            var adxValue = _adx.ADX[currentIndex];
            var diPlus = _adx.DIPlus[currentIndex];
            var diMinus = _adx.DIMinus[currentIndex];
            
            var bbUpper = _bb.Top[currentIndex];
            var bbLower = _bb.Bottom[currentIndex];
            var bbMiddle = _bb.Main[currentIndex];
            
            var rsiValue = _rsi.Result[currentIndex];
            var close = Bars.ClosePrices[currentIndex];
            
            // 成交量确认
            if (EnableVolumeFilter && !IsVolumeConfirmed())
                return TradeType.Buy; // 返回默认值，不会执行

            // 判断市场状态
            bool isTrendingMarket = adxValue > AdxThreshold;
            
            if (isTrendingMarket)
            {
                return GenerateTrendSignal(diPlus, diMinus, close, bbUpper, bbLower, rsiValue);
            }
            else
            {
                return GenerateRangeSignal(close, bbUpper, bbLower, bbMiddle, rsiValue);
            }
        }

        private TradeType GenerateTrendSignal(double diPlus, double diMinus, double close, double bbUpper, double bbLower, double rsiValue)
        {
            bool bullishTrend = diPlus > diMinus;
            bool bearishTrend = diMinus > diPlus;
            
            // 趋势突破信号 + RSI 过滤
            if (bullishTrend && close > bbUpper && rsiValue < 70) // 避免超买区域
            {
                Print($"趋势做多信号: DI+({diPlus:F1}) > DI-({diMinus:F1}), 价格突破上轨, RSI={rsiValue:F1}");
                return TradeType.Buy;
            }
            
            if (bearishTrend && close < bbLower && rsiValue > 30) // 避免超卖区域
            {
                Print($"趋势做空信号: DI-({diMinus:F1}) > DI+({diPlus:F1}), 价格突破下轨, RSI={rsiValue:F1}");
                return TradeType.Sell;
            }
            
            return (TradeType)int.MaxValue; // 返回一个无效值，避免意外触发
        }

        private TradeType GenerateRangeSignal(double close, double bbUpper, double bbLower, double bbMiddle, double rsiValue)
        {
            var currentIndex = Bars.Count - 1;
            var previousRsiValue = _rsi.Result[currentIndex - 1];

            // 震荡区间均值回归策略
            
            // 做空信号：RSI从70以上，下穿回70，作为确认
            if (previousRsiValue > 70 && rsiValue <= 70)
            {
                Print($"区间做空信号: RSI下穿70确认 (前值={previousRsiValue:F1}, 当前值={rsiValue:F1})");
                return TradeType.Sell;
            }
            
            // 做多信号：RSI从30以下，上穿回30，作为确认
            if (previousRsiValue < 30 && rsiValue >= 30)
            {
                Print($"区间做多信号: RSI上穿30确认 (前值={previousRsiValue:F1}, 当前值={rsiValue:F1})");
                return TradeType.Buy;
            }
            
            return (TradeType)int.MaxValue; // 返回一个无效值，避免意外触发
        }

        private bool IsVolumeConfirmed()
        {
            if (!EnableVolumeFilter)
                return true;
                
            var currentIndex = Bars.Count - 1;
            var currentVolume = Bars.TickVolumes[currentIndex];
            
            // 简化成交量确认：检查当前成交量是否大于过去10根K线的平均值
            if (currentIndex < 10)
                return true;
                
            var avgVolume = 0.0;
            for (int i = 1; i <= 10; i++)
            {
                avgVolume += Bars.TickVolumes[currentIndex - i];
            }
            avgVolume /= 10.0;
            
            // 要求当前成交量至少是平均值的 1.2 倍
            return currentVolume > avgVolume * 1.2;
        }
        #endregion

        #region Trade Execution & Sizing
        private void ExecuteTrade(TradeType tradeType)
        {
            // 1. 计算风险金额
            var riskAmount = Account.Balance * (RiskPerTrade / 100);
            if (EnableKellyCriterion)
            {
                var kellyFraction = CalculateKellyFraction();
                if (kellyFraction > 0)
                {
                    riskAmount = Account.Balance * kellyFraction;
                    Print($"凯利公式已启用。推荐风险金额: ${riskAmount:F2}");
                }
                else
                {
                    Print("凯利公式计算风险为0，跳过交易。");
                    return;
                }
            }

            if (riskAmount <= 0)
            {
                Print("风险金额为零或负数，跳过交易。");
                return;
            }

            // 2. 计算止损距离 (Pips)
            var stopLossInPips = (_atr.Result.LastValue / Symbol.PipSize) * AtrSlMultiplier;
            if (stopLossInPips <= 0)
            {
                Print("ATR计算的止损距离为0，无法设置止损，跳过交易。");
                return;
            }

            // 3. 计算仓位 (Lots)
            var volumeInLots = CalculateVolumeInLots(riskAmount, stopLossInPips);
            if (volumeInLots <= 0)
            {
                // 日志已在CalculateVolumeInLots中打印
                return;
            }

            // 4. 执行订单
            Print($"准备下单: 类型={tradeType}, 手数={volumeInLots:F2}, SL(pips)={stopLossInPips:F1}");
            var result = ExecuteMarketOrder(tradeType, Symbol.Name, volumeInLots, LABEL, stopLossInPips, null);

            if (result.IsSuccessful)
            {
                var position = result.Position;
                _lastTradeDate = Server.Time.Date;
                var riskTaken = (riskAmount / Account.Balance) * 100;
                _dailyRisk += riskTaken;
                Print($"交易执行成功: PosID={position.Id}, 手数={position.Quantity:F2}, 风险={riskTaken:F2}%, 当日累计风险={_dailyRisk:F2}%");
            }
            else
            {
                Print($"交易执行失败: {result.Error}");
            }
        }

        private double CalculateKellyFraction()
        {
            if (KellyRiskRewardRatio <= 0) return 0;
            var kellyFraction = KellyWinRate - ((1 - KellyWinRate) / KellyRiskRewardRatio);
            var safeKelly = Math.Max(0, kellyFraction * KellySafetyFactor);
            return safeKelly;
        }

        private double CalculateVolumeInLots(double riskAmount, double stopLossPips)
        {
            if (stopLossPips <= 0)
            {
                Print("错误: 止损点数 (pips) 必须为正数。");
                return 0;
            }

            // 计算1标准手的止损价值
            double stopLossDistanceInPrice = stopLossPips * Symbol.PipSize;
            double stopLossInTicks = stopLossDistanceInPrice / Symbol.TickSize;
            double stopLossValuePerLot = stopLossInTicks * Symbol.TickValue;

            if (stopLossValuePerLot <= 0)
            {
                Print($"错误: 计算出的每手止损价值为零或负数 ({stopLossValuePerLot:F2})。");
                return 0;
            }

            // 根据风险金额计算所需的手数
            double volumeInLots = riskAmount / stopLossValuePerLot;

            // 警告修复：遵循最新的API规范，使用基于单位的流程进行规范化
            // 1. 手数 -> 单位
            long volumeInUnits = (long)Symbol.QuantityToVolumeInUnits(volumeInLots);
            // 2. 规范化单位
            long normalizedVolumeInUnits = (long)Symbol.NormalizeVolumeInUnits(volumeInUnits, RoundingMode.Down);
            // 3. 规范化单位 -> 手数
            double normalizedVolumeInLots = Symbol.VolumeInUnitsToQuantity(normalizedVolumeInUnits);

            Print($"仓位计算: 风险金额=${riskAmount:F2}, 止损(Pips)={stopLossPips:F1}, 每手止损价值=${stopLossValuePerLot:F2}, 计算手数={volumeInLots:F4}, 规范化手数={normalizedVolumeInLots:F2}");

            if (normalizedVolumeInLots <= 0)
            {
                Print("警告: 规范化后的交易手数为0。可能由于风险设置过低或止损距离过大。");
            }

            return normalizedVolumeInLots;
        }
        #endregion

        #region Position Management
        private void ManageExistingPosition(Position position)
        {
            // 简单的追踪止损逻辑
            var currentIndex = Bars.Count - 1;
            var atrValue = _atr.Result[currentIndex];
            var close = Bars.ClosePrices[currentIndex];
            
            if (position.TradeType == TradeType.Buy)
            {
                var newStopLoss = close - (atrValue * AtrSlMultiplier);
                if (position.StopLoss.HasValue && newStopLoss > position.StopLoss.Value)
                {
                    try
                    {
                        // 使用Position对象的ModifyStopLossPrice方法，避免ProtectionType参数问题
                        var result = position.ModifyStopLossPrice(newStopLoss);
                        if (result.IsSuccessful)
                            Print($"追踪止损更新: {newStopLoss:F2}");
                        else
                            Print($"修改仓位失败: {result.Error}");
                    }
                    catch (Exception ex)
                    {
                        Print($"修改仓位异常: {ex.Message}");
                    }
                }
            }
            else
            {
                var newStopLoss = close + (atrValue * AtrSlMultiplier);
                if (position.StopLoss.HasValue && newStopLoss < position.StopLoss.Value)
                {
                    try
                    {
                        // 使用Position对象的ModifyStopLossPrice方法，避免ProtectionType参数问题
                        var result = position.ModifyStopLossPrice(newStopLoss);
                        if (result.IsSuccessful)
                            Print($"追踪止损更新: {newStopLoss:F2}");
                        else
                            Print($"修改仓位失败: {result.Error}");
                    }
                    catch (Exception ex)
                    {
                        Print($"修改仓位异常: {ex.Message}");
                    }
                }
            }
        }
        #endregion

        #region Utility Methods
        private void ResetDailyRiskIfNeeded()
        {
            if (Server.Time.Date != _lastTradeDate)
            {
                _dailyRisk = 0;
                Print($"新交易日开始，重置每日风险计数");
                _lastTradeDate = Server.Time.Date;
            }
        }

        protected override void OnStop()
        {
            Print("AdaptiveMultiModeCbot V2 Fixed 停止");
        }
        #endregion
    }
} 