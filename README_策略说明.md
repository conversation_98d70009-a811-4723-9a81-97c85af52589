# Hummingbot交易策略集合

## 项目简介

本项目包含从Hummingbot开源项目下载的完整交易策略集合。Hummingbot是一个开源的算法交易软件框架，支持在多个中心化和去中心化交易所上运行自动化交易策略。

## 目录结构

```
hummingbot_strategies/
├── v1_strategies/          # V1版本经典策略
├── v2_strategies/          # V2版本策略框架  
├── scripts/               # 脚本策略示例
├── controllers/           # 控制器组件
├── connectors/            # 交易所连接器
└── documentation/         # 项目文档
```

## V1策略（经典策略）

### 做市策略
- **Pure Market Making (PMM)** - 纯做市策略
  - 在买卖两侧同时下单，从价差中获利
  - 适用于流动性提供和市场做市

- **Avellaneda Market Making** - 基于Avellaneda-Stoikov模型的高级做市策略
  - 使用数学模型优化订单定价
  - 根据波动率和库存风险动态调整策略

- **Perpetual Market Making** - 永续合约做市策略
  - 专门用于永续合约市场的做市策略

### 套利策略
- **Cross-Exchange Market Making (XEMM)** - 跨交易所做市
  - 在一个交易所下单，在另一个交易所对冲
  - 利用跨交易所价差获利

- **AMM Arbitrage** - 自动做市商套利
  - 在AMM和订单簿交易所之间进行套利

- **Spot Perpetual Arbitrage** - 现货永续套利
  - 在现货和永续合约之间进行套利

### 其他策略
- **Liquidity Mining** - 流动性挖矿
  - 为特定交易对提供流动性获得奖励

- **TWAP** - 时间加权平均价格
  - 将大单分解为小单，在时间窗口内均匀执行

- **Hedge** - 对冲策略
  - 用于风险管理和对冲操作

- **Cross-Exchange Mining** - 跨交易所挖矿
  - 跨多个交易所的流动性挖矿策略

## V2策略框架（现代化策略）

### 核心组件
- **Controllers** - 策略控制器
  - 市场做市控制器基础类
  - 方向性交易控制器基础类
  - 控制器基础类

- **Executors** - 执行器
  - Position Executor - 仓位执行器
  - DCA Executor - 定投执行器  
  - Grid Executor - 网格执行器
  - TWAP Executor - TWAP执行器
  - XEMM Executor - 跨交易所执行器
  - Order Executor - 订单执行器
  - Arbitrage Executor - 套利执行器

- **Models** - 数据模型
  - 执行器信息模型
  - 执行器动作模型
  - 基础数据模型

- **Utils** - 工具函数
  - 通用函数
  - 配置编码解码器
  - 分布函数
  - 订单级别构建器

- **Backtesting** - 回测框架
  - 回测引擎基础类
  - 回测数据提供者
  - 执行器模拟器

## 脚本策略示例

### 基础示例
- `simple_pmm.py` - 简单做市示例
- `simple_vwap.py` - 简单VWAP示例
- `simple_xemm.py` - 简单跨交易所做市示例

### V2策略示例
- `v2_directional_rsi.py` - 基于RSI的方向性策略
- `v2_funding_rate_arb.py` - 资金费率套利策略
- `v2_twap_multiple_pairs.py` - 多交易对TWAP策略
- `v2_with_controllers.py` - 使用控制器的V2策略

### 社区策略
- `directional_strategy_rsi_spot.py` - RSI方向性策略
- `macd_bb_directional_strategy.py` - MACD布林带策略
- `fixed_grid.py` - 固定网格策略
- `simple_arbitrage_example.py` - 简单套利示例
- `triangular_arbitrage.py` - 三角套利策略

### 实用工具
- `download_candles.py` - 下载K线数据
- `backtest_mm_example.py` - 做市策略回测示例
- `liquidations_example.py` - 清算监控示例

## 控制器组件

### 方向性交易控制器
- `bollinger_v1.py` - 布林带策略控制器
- `macd_bb_v1.py` - MACD布林带控制器
- `supertrend_v1.py` - SuperTrend控制器
- `dman_v3.py` - DMan V3控制器

### 通用控制器
- `pmm.py` - 做市控制器
- `arbitrage_controller.py` - 套利控制器
- `grid_strike.py` - 网格策略控制器
- `xemm_multiple_levels.py` - 多层跨交易所做市

### 做市控制器
- `pmm_simple.py` - 简单做市控制器
- `pmm_dynamic.py` - 动态做市控制器
- `dman_maker_v2.py` - DMan做市控制器V2

## 支持的交易所

### 中心化交易所 (CEX)
- Binance（币安）
- OKX
- Gate.io
- KuCoin（库币）
- Bybit
- HTX（原火币）
- Kraken
- Coinbase Advanced Trade
- 等等...

### 去中心化交易所 (DEX)
- Uniswap
- dYdX V4
- Hyperliquid
- XRPL（瑞波账本）
- Balancer
- Curve
- PancakeSwap
- SushiSwap
- 等等...

## 使用说明

### 环境要求
- Python 3.8+
- 安装Hummingbot框架
- 配置相应的交易所API密钥

### 快速开始
1. 选择合适的策略类型
2. 复制对应的策略文件
3. 根据需要修改配置参数
4. 在Hummingbot客户端中运行策略

### 配置示例
每个策略都有相应的配置文件，包含：
- 交易所选择
- 交易对设置
- 风险参数
- 执行参数

## 许可证

本项目遵循Apache 2.0开源许可证。

## 相关资源

- [Hummingbot官方网站](https://hummingbot.org/)
- [Hummingbot文档](https://docs.hummingbot.org/)
- [GitHub仓库](https://github.com/hummingbot/hummingbot)
- [社区Discord](https://discord.gg/hummingbot)

## 注意事项

⚠️ **风险提示**：
- 算法交易存在风险，请在充分了解策略原理后使用
- 建议先在测试环境中验证策略效果
- 务必设置合理的风险控制参数
- 密切监控策略运行状态

## 更新日期

本策略集合下载于：2024年12月 