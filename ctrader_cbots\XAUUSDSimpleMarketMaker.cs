using System;
using System.Linq;
using cAlgo.API;
using cAlgo.API.Indicators;

namespace cAlgo.Robots
{
    /// <summary>
    /// XAUUSD简化版做市机器人
    /// 专门针对黄金市场特性优化，确保cTrader兼容性
    /// </summary>
    [Robot(TimeZone = TimeZones.UTC, AccessRights = AccessRights.None)]
    public class XAUUSDSimpleMarketMaker : Robot
    {
        #region 参数设置
        [Parameter("启用策略", DefaultValue = true)]
        public bool EnableStrategy { get; set; }

        [Parameter("订单量 (盎司)", DefaultValue = 0.1, MinValue = 0.01, MaxValue = 10.0)]
        public double OrderSize { get; set; }

        [Parameter("基础价差 (点)", DefaultValue = 50, MinValue = 10, MaxValue = 200)]
        public double BaseSpread { get; set; }

        [Parameter("最大持仓 (盎司)", DefaultValue = 2.0, MinValue = 0.1, MaxValue = 20.0)]
        public double MaxPosition { get; set; }

        [Parameter("ATR周期", DefaultValue = 20, MinValue = 10, MaxValue = 50)]
        public int AtrPeriod { get; set; }

        [Parameter("波动率倍数", DefaultValue = 1.5, MinValue = 1.0, MaxValue = 3.0)]
        public double VolatilityMultiplier { get; set; }
        #endregion

        #region 私有字段
        private AverageTrueRange _atr;
        private const string LABEL = "XAUUSD_MM";
        private DateTime _lastOrderTime;
        #endregion

        #region 初始化
        protected override void OnStart()
        {
            Print($"XAUUSD简化做市机器人启动 - {DateTime.UtcNow:yyyy-MM-dd HH:mm:ss} UTC");
            
            // 验证交易品种
            if (!Symbol.Name.Contains("XAU"))
            {
                Print($"警告: 此机器人专为XAUUSD设计，当前品种: {Symbol.Name}");
            }
            
            // 初始化指标
            _atr = Indicators.AverageTrueRange(AtrPeriod, MovingAverageType.Exponential);
            _lastOrderTime = DateTime.MinValue;
            
            Print($"初始化完成 - 订单量: {OrderSize}, 基础价差: {BaseSpread}点");
        }
        #endregion

        #region 主要逻辑
        protected override void OnTick()
        {
            if (!EnableStrategy)
                return;

            try
            {
                // 限制下单频率 (每5秒最多一次)
                if ((DateTime.UtcNow - _lastOrderTime).TotalSeconds < 5)
                    return;

                // 检查风险限制
                if (!CheckRiskLimits())
                    return;

                // 执行做市
                ExecuteMarketMaking();
                
                _lastOrderTime = DateTime.UtcNow;
            }
            catch (Exception ex)
            {
                Print($"OnTick错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 检查风险限制
        /// </summary>
        private bool CheckRiskLimits()
        {
            var netPosition = GetNetPosition();
            
            if (Math.Abs(netPosition) >= MaxPosition)
            {
                Print($"触发最大持仓限制: {Math.Abs(netPosition):F2} >= {MaxPosition:F2}");
                return false;
            }
            
            return true;
        }

        /// <summary>
        /// 执行做市逻辑
        /// </summary>
        private void ExecuteMarketMaking()
        {
            // 计算动态价差
            var currentSpread = CalculateDynamicSpread();
            var spreadInPrice = currentSpread * Symbol.PipSize;
            
            // 获取当前价格
            var midPrice = (Symbol.Bid + Symbol.Ask) / 2;
            var bidPrice = midPrice - spreadInPrice / 2;
            var askPrice = midPrice + spreadInPrice / 2;
            
            // 计算订单量
            var netPosition = GetNetPosition();
            var buySize = CalculateOrderSize(TradeType.Buy, netPosition);
            var sellSize = CalculateOrderSize(TradeType.Sell, netPosition);
            
            // 取消现有订单
            CancelAllOrders();
            
            // 下新订单
            if (buySize > 0)
            {
                var volumeInUnits = Symbol.QuantityToVolumeInUnits(buySize);
                PlaceLimitOrder(TradeType.Buy, Symbol.Name, volumeInUnits, bidPrice, LABEL + "_BUY");
            }
            
            if (sellSize > 0)
            {
                var volumeInUnits = Symbol.QuantityToVolumeInUnits(sellSize);
                PlaceLimitOrder(TradeType.Sell, Symbol.Name, volumeInUnits, askPrice, LABEL + "_SELL");
            }
            
            // 输出状态
            Print($"做市更新 - 价差:{currentSpread:F1}点, 买:{bidPrice:F2}({buySize:F2}), " +
                  $"卖:{askPrice:F2}({sellSize:F2}), 净持仓:{netPosition:F2}");
        }

        /// <summary>
        /// 计算动态价差
        /// </summary>
        private double CalculateDynamicSpread()
        {
            var baseSpread = BaseSpread;
            
            // 基于ATR调整价差
            if (_atr.Result.LastValue > 0)
            {
                var atrInPips = _atr.Result.LastValue / Symbol.PipSize;
                var avgAtr = 100; // 假设平均ATR为100点
                
                if (atrInPips > avgAtr * 1.5) // 高波动
                {
                    baseSpread *= VolatilityMultiplier;
                }
                else if (atrInPips < avgAtr * 0.5) // 低波动
                {
                    baseSpread *= 0.8;
                }
            }
            
            return Math.Max(20, Math.Min(300, baseSpread)); // 限制在20-300点之间
        }

        /// <summary>
        /// 计算订单量
        /// </summary>
        private double CalculateOrderSize(TradeType tradeType, double netPosition)
        {
            var baseSize = OrderSize;
            var positionRatio = netPosition / MaxPosition;
            
            // 基于持仓调整订单量
            if (tradeType == TradeType.Buy && positionRatio > 0.2)
            {
                baseSize *= 0.5; // 已有多头持仓，减少买单
            }
            else if (tradeType == TradeType.Sell && positionRatio < -0.2)
            {
                baseSize *= 0.5; // 已有空头持仓，减少卖单
            }
            
            return Math.Max(0.01, baseSize);
        }

        /// <summary>
        /// 获取净持仓
        /// </summary>
        private double GetNetPosition()
        {
            var positions = Positions.Where(p => p.Label.StartsWith(LABEL));
            double netVolume = 0;
            
            foreach (var position in positions)
            {
                if (position.TradeType == TradeType.Buy)
                    netVolume += position.VolumeInUnits;
                else
                    netVolume -= position.VolumeInUnits;
            }
            
            return netVolume / 100000; // 转换为盎司
        }

        /// <summary>
        /// 取消所有订单
        /// </summary>
        private void CancelAllOrders()
        {
            var orders = PendingOrders.Where(o => o.Label.StartsWith(LABEL)).ToList();
            foreach (var order in orders)
            {
                CancelPendingOrder(order);
            }
        }
        #endregion

        #region 事件处理
        protected override void OnStop()
        {
            Print("XAUUSD简化做市机器人停止");
            CancelAllOrders();
            
            var netPosition = GetNetPosition();
            Print($"最终净持仓: {netPosition:F2}盎司");
        }

        protected override void OnBar()
        {
            // 每小时报告一次
            if (Bars.TimeFrame == TimeFrame.Hour)
            {
                var netPosition = GetNetPosition();
                var atrInPips = _atr.Result.LastValue / Symbol.PipSize;
                
                Print($"小时报告 - 净持仓:{netPosition:F2}盎司, ATR:{atrInPips:F1}点, " +
                      $"活跃订单:{PendingOrders.Count(o => o.Label.StartsWith(LABEL))}个");
            }
        }
        #endregion
    }
}
