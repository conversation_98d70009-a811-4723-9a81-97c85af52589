using System;
using System.Collections.Generic;
using System.Linq;
using cAlgo.API;
using cAlgo.API.Indicators;
using cAlgo.API.Internals;

/*
 * AdaptiveMultiModeCbot
 * --------------------------------------------------
 * 架构概览：
 * Core            : 负责 Robot 生命周期、定时调度、模块装配
 * Signal          : 提供交易信号（趋势 / 震荡 / 其他因子）
 * Execution       : 负责下单、修改、平仓等操作，兼顾滑点与成本评估
 * Risk            : 统一仓位 sizing、最大风险敞口、止损/止盈/移动 SL
 * Analytics       : 记录日志、统计 KPI、输出回测 / 实盘报告
 * --------------------------------------------------
 * 现阶段仅实现：
 * 1. RegimeDetector (简单 ATR 比值 + ADX)  
 * 2. TrendSignalProvider & RangeSignalProvider (Bollinger + Donchian)  
 * 3. BasicRiskManager (固定风险百分比)  
 * 4. ExecutionService (封装下单)  
 * 5. TradeLogger (写入日志)
 * --------------------------------------------------
 * TODO: 待后续迭代完善 multi-level grid, pyramiding, advanced analytics ...
 */

namespace cAlgo
{
    [Robot(TimeZone = TimeZones.UTC, AccessRights = AccessRights.None)]
    public class AdaptiveMultiModeCbot : Robot
    {
        // === 参数 ===
        [Parameter("风险占用 %", DefaultValue = 1.0, MinValue = 0.1)]
        public double RiskPercent { get; set; }

        [Parameter("ATR 周期", DefaultValue = 14)]
        public int AtrPeriod { get; set; }

        [Parameter("ADX 周期", DefaultValue = 14)]
        public int AdxPeriod { get; set; }

        [Parameter("趋势 ADX 阈值", DefaultValue = 25)]
        public double AdxTrendThreshold { get; set; }

        [Parameter("Donchian 周期", DefaultValue = 20)]
        public int DonchianPeriod { get; set; }

        [Parameter("BB 周期", DefaultValue = 20)]
        public int BbPeriod { get; set; }

        [Parameter("BB 偏差", DefaultValue = 2.5)]
        public double BbStd { get; set; }

        [Parameter("最大敞口 (手)", DefaultValue = 0.3, MinValue = 0.01)]
        public double MaxTotalLots { get; set; }

        // === 模块实例 ===
        private IRegimeDetector _regimeDetector;
        private ISignalProvider _trendSignal;
        private ISignalProvider _rangeSignal;
        private IRiskManager _riskManager;
        private IExecutionService _execution;
        private ITradeLogger _logger;

        // === 指标缓存 ===
        private AverageDirectionalMovementIndexRating _adx;
        private AverageTrueRange _atr;
        private BollingerBands _bb;
        private DonchianChannel _donchian;

        protected override void OnStart()
        {
            // 初始化指标
            _atr = Indicators.AverageTrueRange(AtrPeriod, MovingAverageType.Simple);
            _adx = Indicators.AverageDirectionalMovementIndexRating(AdxPeriod);
            _bb = Indicators.BollingerBands(Bars.ClosePrices, BbPeriod, BbStd, MovingAverageType.Simple);
            _donchian = Indicators.DonchianChannel(DonchianPeriod);

            // 创建模块
            _regimeDetector = new RegimeDetector(_atr, _adx, AdxTrendThreshold, this);
            _trendSignal = new TrendSignalProvider(_bb, _donchian, this);
            _rangeSignal = new RangeSignalProvider(_bb, this);
            _riskManager = new BasicRiskManager(RiskPercent, MaxTotalLots, this);
            _execution = new ExecutionService(this);
            _logger = new TradeLogger(this);

            Print("AdaptiveMultiModeCbot started - TimeFrame: {0}", TimeFrame);
        }

        protected override void OnBar()
        {
            Regime regime = _regimeDetector.GetRegime();
            TradeSignal? signal = null;

            switch (regime)
            {
                case Regime.Trend:
                    signal = _trendSignal.GetSignal();
                    break;
                case Regime.Range:
                    signal = _rangeSignal.GetSignal();
                    break;
            }

            if (signal.HasValue)
            {
                ExecuteTrade(signal.Value);
            }
        }

        private void ExecuteTrade(TradeSignal tradeSignal)
        {
            // 检查最大敞口
            if (!_riskManager.CanOpenNewPosition(tradeSignal.TradeType))
                return;

            double slPips = tradeSignal.SLPips;
            double tpPips = tradeSignal.TPPips;
            double volume = _riskManager.CalculateVolume(slPips);

            var result = _execution.PlaceMarketOrder(tradeSignal.TradeType, volume, slPips, tpPips, tradeSignal.Label);
            _logger.LogTradeSignal(tradeSignal, result);
        }

        #region --- Core Interfaces & Implementations ---

        // Regime Enum
        private enum Regime { Trend, Range }

        // TradeSignal Struct
        private struct TradeSignal
        {
            public TradeType TradeType;
            public double SLPips;
            public double TPPips;
            public string Label;
        }

        // === Interfaces ===
        private interface IRegimeDetector { Regime GetRegime(); }
        private interface ISignalProvider { TradeSignal? GetSignal(); }
        private interface IRiskManager
        {
            double CalculateVolume(double slPips);
            bool CanOpenNewPosition(TradeType tradeType);
        }
        private interface IExecutionService
        {
            TradeResult PlaceMarketOrder(TradeType side, double volume, double slPips, double tpPips, string label);
        }
        private interface ITradeLogger
        {
            void LogTradeSignal(TradeSignal signal, TradeResult result);
        }

        // === Implementations ===

        private class RegimeDetector : IRegimeDetector
        {
            private readonly AverageTrueRange _atr;
            private readonly AverageDirectionalMovementIndexRating _adx;
            private readonly double _adxThreshold;
            private readonly AdaptiveMultiModeCbot _bot;

            public RegimeDetector(AverageTrueRange atr, AverageDirectionalMovementIndexRating adx, double adxThreshold, AdaptiveMultiModeCbot bot)
            {
                _atr = atr;
                _adx = adx;
                _adxThreshold = adxThreshold;
                _bot = bot;
            }

            public Regime GetRegime()
            {
                double adxrVal = _adx.ADXR.Last(0);
                return adxrVal >= _adxThreshold ? Regime.Trend : Regime.Range;
            }
        }

        private class TrendSignalProvider : ISignalProvider
        {
            private readonly BollingerBands _bb;
            private readonly DonchianChannel _donchian;
            private readonly AdaptiveMultiModeCbot _bot;

            public TrendSignalProvider(BollingerBands bb, DonchianChannel donchian, AdaptiveMultiModeCbot bot)
            {
                _bb = bb;
                _donchian = donchian;
                _bot = bot;
            }

            public TradeSignal? GetSignal()
            {
                double upper = _bb.Top.LastValue;
                double lower = _bb.Bottom.LastValue;
                double close = _bot.Bars.ClosePrices.Last(0);

                // 上轨突破趋势做多
                if (close > upper)
                {
                    double slDistance = Math.Abs(close - _donchian.Bottom.LastValue);
                    double tpDistance = Math.Abs(_donchian.Top.LastValue - close);
                    
                    // 确保 SL/TP 有效
                    if (slDistance <= 0 || tpDistance <= 0) return null;
                    
                    return new TradeSignal
                    {
                        TradeType = TradeType.Buy,
                        SLPips = slDistance / _bot.Symbol.PipSize,
                        TPPips = tpDistance / _bot.Symbol.PipSize,
                        Label = "TrendBuy"
                    };
                }
                // 下轨突破趋势做空
                if (close < lower)
                {
                    double slDistance = Math.Abs(_donchian.Top.LastValue - close);
                    double tpDistance = Math.Abs(close - _donchian.Bottom.LastValue);
                    
                    // 确保 SL/TP 有效
                    if (slDistance <= 0 || tpDistance <= 0) return null;
                    
                    return new TradeSignal
                    {
                        TradeType = TradeType.Sell,
                        SLPips = slDistance / _bot.Symbol.PipSize,
                        TPPips = tpDistance / _bot.Symbol.PipSize,
                        Label = "TrendSell"
                    };
                }
                return null;
            }
        }

        private class RangeSignalProvider : ISignalProvider
        {
            private readonly BollingerBands _bb;
            private readonly AdaptiveMultiModeCbot _bot;

            public RangeSignalProvider(BollingerBands bb, AdaptiveMultiModeCbot bot)
            {
                _bb = bb;
                _bot = bot;
            }

            public TradeSignal? GetSignal()
            {
                double upper = _bb.Top.LastValue;
                double lower = _bb.Bottom.LastValue;
                double middle = _bb.Main.LastValue;
                double close = _bot.Bars.ClosePrices.Last(0);

                if (close > upper) // 上轨超买做空
                {
                    double slDistance = Math.Abs(close - upper) * 2;
                    double tpDistance = Math.Abs(close - middle);
                    
                    // 确保 SL/TP 有效
                    if (slDistance <= 0 || tpDistance <= 0) return null;
                    
                    return new TradeSignal
                    {
                        TradeType = TradeType.Sell,
                        SLPips = slDistance / _bot.Symbol.PipSize,
                        TPPips = tpDistance / _bot.Symbol.PipSize,
                        Label = "RangeSell"
                    };
                }
                if (close < lower) // 下轨超卖做多
                {
                    double slDistance = Math.Abs(lower - close) * 2;
                    double tpDistance = Math.Abs(middle - close);
                    
                    // 确保 SL/TP 有效
                    if (slDistance <= 0 || tpDistance <= 0) return null;
                    
                    return new TradeSignal
                    {
                        TradeType = TradeType.Buy,
                        SLPips = slDistance / _bot.Symbol.PipSize,
                        TPPips = tpDistance / _bot.Symbol.PipSize,
                        Label = "RangeBuy"
                    };
                }
                return null;
            }
        }

        private class BasicRiskManager : IRiskManager
        {
            private readonly double _riskPercent;
            private readonly double _maxTotalLots;
            private readonly AdaptiveMultiModeCbot _bot;

            public BasicRiskManager(double riskPercent, double maxTotalLots, AdaptiveMultiModeCbot bot)
            {
                _riskPercent = riskPercent;
                _maxTotalLots = maxTotalLots;
                _bot = bot;
            }

            public double CalculateVolume(double slPips)
            {
                // 防止除零和负值
                if (slPips <= 0) return 0.01; // 最小手数
                
                double accountRisk = _bot.Account.Balance * _riskPercent / 100.0;
                double valuePerLotPerPip = _bot.Symbol.PipValue * 100000; // 1 lot = 100k units
                double lots = accountRisk / (Math.Abs(slPips) * valuePerLotPerPip);
                
                // 确保在合理范围内
                lots = Math.Max(0.01, lots); // 最小 0.01 手
                lots = Math.Min(lots, _maxTotalLots); // 最大限制
                
                return lots;
            }

            public bool CanOpenNewPosition(TradeType tradeType)
            {
                double currentLots = _bot.Positions.Where(p => p.SymbolName == _bot.SymbolName).Sum(p => p.VolumeInUnits) / _bot.Symbol.QuantityToVolumeInUnits(1);
                return currentLots < _maxTotalLots;
            }
        }

        private class ExecutionService : IExecutionService
        {
            private readonly AdaptiveMultiModeCbot _bot;
            public ExecutionService(AdaptiveMultiModeCbot bot) { _bot = bot; }

            public TradeResult PlaceMarketOrder(TradeType side, double volume, double slPips, double tpPips, string label)
            {
                long units = (long)_bot.Symbol.QuantityToVolumeInUnits(volume);
                return _bot.ExecuteMarketOrder(side, _bot.SymbolName, units, label, slPips, tpPips);
            }
        }

        private class TradeLogger : ITradeLogger
        {
            private readonly AdaptiveMultiModeCbot _bot;
            public TradeLogger(AdaptiveMultiModeCbot bot) { _bot = bot; }

            public void LogTradeSignal(TradeSignal signal, TradeResult result)
            {
                if (result.IsSuccessful)
                {
                    _bot.Print("{0} executed: PosID {1} lots:{2:F2} {3} SL:{4} TP:{5}", signal.Label, result.Position?.Id, result.Position?.VolumeInUnits / _bot.Symbol.QuantityToVolumeInUnits(1), signal.TradeType, signal.SLPips, signal.TPPips);
                }
                else
                {
                    _bot.Print("{0} FAILED: {1}", signal.Label, result.Error);
                }
            }
        }

        #endregion
    }
} 