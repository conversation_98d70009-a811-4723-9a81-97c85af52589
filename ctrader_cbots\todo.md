# cTrader 黄金 & US500 CBot 开发 Todo

## 项目概述
基于 hummingbot_strategies 仓库开发适用于 cTrader 平台的 XAUUSD 和 US500 自动化交易机器人

## ✅ 已完成阶段

### 阶段 1：项目架构设计 ✅
- [x] 分析 hummingbot 策略结构
- [x] 调研 cTrader Algo API 
- [x] 确定技术栈：C# .NET 6
- [x] 制定开发计划

### 阶段 2：基础策略开发 ✅
- [x] 创建 DualModeCbot.cs（初版，有问题）
- [x] 设计 AdaptiveMultiModeCbot.cs（架构重构）
- [x] 解决编译错误
- [x] 完成 AdaptiveMultiModeCbot_V2_Fixed.cs

### 阶段 2.1：编译错误修复 ✅ 
- [x] 修复 CS0266 类型转换错误
- [x] 修复 CS0618 过时API警告  
- [x] 修复 CS1501 参数重载错误
- [x] 优化 Symbol.NormalizeVolumeInUnits 调用
- [x] 确认 ModifyPosition vs ModifyPositionAsync API选择
- [x] 解决 ProtectionType 兼容性问题
- [x] 确保云端运行稳定性
- [x] 更新编译错误修复报告

## 🔄 当前状态：策略重构与优化

### 阶段 3：回测分析与策略重构 ✅
- [x] **分析V1回测失败原因**：识别出风险管理失控和信号质量差两大问题。
- [x] **修复致命Bug**：重构仓位计算逻辑，确保风险严格按设定执行。
- [x] **提升信号质量**：增加200 EMA作为长期趋势过滤器，避免逆势交易。
- [x] **更新策略文档**：在`todo.md`中记录重构决策。

## 📋 进行中任务

### 阶段 4：高级功能集成 (进行中)
- [x] **引入凯利公式**：实现动态仓位管理。
  - [x] 添加凯利公式相关参数（启用开关、胜率、赔率、安全因子）。
  - [x] 创建 `CalculateKellyFraction` 方法。
  - [x] 将凯利逻辑整合到 `ExecuteTrade` 方法中。
- [ ] **性能回测**：验证凯利公式在不同市场环境下的表现。

### 阶段 5：最终验证与多品种扩展
- [ ] 基于凯利公式，重新进行参数优化。
- [ ] 将策略（包括凯利管理）适配到US500。
- [ ] 探索更多高级功能（如动态止盈、市场状态自适应等）。

## 🎯 下一步行动计划

### 立即执行（本周）
1. **启动回测**：
   - 使用 AdaptiveMultiModeCbot_V2_Fixed.cs
   - XAUUSD M15，默认参数
   - 时间段：2024.01.01-2024.12.31

2. **结果分析**：
   - 记录关键指标
   - 识别失败交易模式  
   - 分析最大回撤原因

3. **快速迭代**：
   - 如果胜率<30%：调整ADX阈值为25
   - 如果回撤>20%：降低风险为0.5%
   - 如果交易过少：扩大交易时段

### 中期目标（本月）
1. **参数优化**：
   - 网格搜索最优参数组合
   - 多时间段回测验证
   - 稳健性测试

2. **策略改进**：
   - 添加止盈分级功能
   - 实现动态止损调整
   - 考虑加仓策略

3. **风险控制增强**：
   - 新闻事件过滤
   - 流动性检测
   - 异常波动保护

## 📊 技术指标

### 当前策略逻辑总结
```
市场状态判断：
├── ADX ≥ 30：趋势市场
│   ├── DI+ > DI- 且 价格突破BB上轨 且 RSI < 70 → 做多
│   └── DI- > DI+ 且 价格突破BB下轨 且 RSI > 30 → 做空
└── ADX < 30：震荡市场  
    ├── 价格从BB上轨回归 且 RSI > 60 → 做空
    └── 价格从BB下轨回归 且 RSI < 40 → 做多

风险管理：
├── 仓位：固定风险金额÷(ATR×2.0×PipValue) 
├── 止损：ATR×2.0
├── 止盈：ATR×3.0 (1:1.5收益比)
└── 每日限制：最大3%风险

过滤条件：
├── 时段：伦敦+纽约会话重叠 (13:00-16:00 UTC)
├── 点差：XAUUSD ≤ 4 pips
└── 成交量：当前 > 过去10根均值×1.2
```

## 🚀 成功指标

### 短期成功（回测阶段）
- [ ] 编译运行无错误
- [ ] 胜率 > 35%  
- [ ] 最大回撤 < 20%
- [ ] 无异常止损距离

### 中期成功（优化阶段）  
- [ ] 年化收益 > 15%
- [ ] 夏普比率 > 1.0
- [ ] 连续亏损 < 5笔
- [ ] 适用于多种市场条件

### 长期成功（实盘阶段）
- [ ] 半年实盘验证成功
- [ ] 适配多品种（XAUUSD + US500）
- [ ] 自动化部署和监控
- [ ] 策略文档和用户手册完成

---

**最后更新**: 2025年1月15日  
**当前版本**: AdaptiveMultiModeCbot_V2_Fixed.cs  
**编译状态**: ✅ 通过  
**下一步**: 回测验证凯利公式的效果，并根据历史数据调整胜率和赔率参数。

# XAUUSD cBot 优化任务清单

### V1.0 - 灾难性回测后分析与修复计划

**状态: 待办**

---

### 第一阶段：核心策略验证与风险控制

- [ ] **任务1：禁用凯利公式，验证固定风险**
  - **描述**: 关闭 `Enable Kelly Criterion` 选项，将 `Risk Per Trade (%)` 设置为 `1`。
  - **目标**: 重新运行回测，验证单笔交易的亏损是否被精确地控制在账户余额的1%左右。确认仓位计算逻辑在固定风险模式下完全正确。
  - **预期结果**: 回测可能依然亏损，但最大回撤应显著降低，不会出现爆仓情况。

### 第二阶段：提升策略信号质量 (Edge)

- [ ] **任务2：优化入场过滤条件**
  - **描述**: 在确认风险控制有效后，通过增加或调整过滤条件来提升入场信号的胜率。
  - **具体想法**:
    - **RSI/Stochastic 信号确认**: 当RSI进入超买/超卖区后，等待其掉头（例如，RSI从低于30反弹到31时再做多），以避免过早入场。
    - **ADX 阈值优化**: 测试不同的 `ADX Threshold` 值（例如 25, 35），找到最适合区分 XAUUSD 趋势与震荡的设定。
    - **多时间周期共振**: 考虑引入更高时间周期的趋势指标（如 H4 的 EMA）作为额外过滤器，确保交易方向与主趋势一致。

- [ ] **任务3：优化指标参数**
  - **描述**: 使用 cTrader 的优化功能，寻找针对 XAUUSD H1 周期更优的指标参数组合。
  - **待优化参数**:
    - `ATR Period` & `ATR SL Multiplier`
    - `BB Period` & `BB StdDev`
    - `RSI Period` & `RSI Levels`
    - `Trend MA Period`

### 第三阶段：重新评估资金管理

- [ ] **任务4：重新引入凯利公式**
  - **描述**: 当核心策略在固定风险下表现出正期望值（Profit Factor > 1）后，再重新启用凯利公式。
  - **目标**: 使用在第二阶段测试出的**真实胜率**和**赔率**作为凯利公式的输入参数，观察其是否能有效放大策略优势，提升长期回报。

--- 