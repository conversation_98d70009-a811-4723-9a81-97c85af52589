using System;
using System.Collections.Generic;
using System.Linq;
using cAlgo.API;
using cAlgo.API.Indicators;

namespace cAlgo.Robots
{
    /// <summary>
    /// XAUUSD高级做市机器人
    /// 基于Hummingbot项目的Pure Market Making和Avellaneda市场策略设计，专门针对黄金市场特性优化，确保cTrader兼容性
    /// </summary>
    [Robot(TimeZone = TimeZones.UTC, AccessRights = AccessRights.None)]
    public class XAUUSDAdvancedMarketMaker : Robot
    {
        #region 基础参数配置
        [Parameter("启用策略", DefaultValue = true, Group = "基础设置")]
        public bool EnableStrategy { get; set; }

        [Parameter("基础订单量 (盎司)", DefaultValue = 0.1, MinValue = 0.01, MaxValue = 10.0, Group = "基础设置")]
        public double BaseOrderSize { get; set; }

        [Parameter("基础价差 (点)", DefaultValue = 50, MinValue = 10, MaxValue = 200, Group = "基础设置")]
        public double BaseSpreadPips { get; set; }

        [Parameter("最大价差 (点)", DefaultValue = 200, MinValue = 50, MaxValue = 500, Group = "基础设置")]
        public double MaxSpreadPips { get; set; }

        [Parameter("最小价差 (点)", DefaultValue = 30, MinValue = 5, MaxValue = 100, Group = "基础设置")]
        public double MinSpreadPips { get; set; }
        #endregion

        #region 风险管理参数
        [Parameter("最大持仓 (盎司)", DefaultValue = 2.0, MinValue = 0.1, MaxValue = 20.0, Group = "风险管理")]
        public double MaxPosition { get; set; }

        [Parameter("日内最大风险 (%)", DefaultValue = 2.0, MinValue = 0.5, MaxValue = 10.0, Group = "风险管理")]
        public double MaxDailyRisk { get; set; }

        [Parameter("库存目标 (%)", DefaultValue = 50.0, MinValue = 0.0, MaxValue = 100.0, Group = "风险管理")]
        public double InventoryTarget { get; set; }

        [Parameter("库存偏斜阈值 (%)", DefaultValue = 20.0, MinValue = 5.0, MaxValue = 50.0, Group = "风险管理")]
        public double InventorySkewThreshold { get; set; }
        #endregion

        #region 动态调整参数
        [Parameter("ATR周期", DefaultValue = 20, MinValue = 10, MaxValue = 50, Group = "动态调整")]
        public int AtrPeriod { get; set; }

        [Parameter("波动率阈值 (点)", DefaultValue = 150, MinValue = 50, MaxValue = 400, Group = "动态调整")]
        public double VolatilityThreshold { get; set; }

        [Parameter("高波动价差倍数", DefaultValue = 2.0, MinValue = 1.2, MaxValue = 5.0, Group = "动态调整")]
        public double HighVolSpreadMultiplier { get; set; }

        [Parameter("低波动价差倍数", DefaultValue = 0.8, MinValue = 0.5, MaxValue = 1.0, Group = "动态调整")]
        public double LowVolSpreadMultiplier { get; set; }
        #endregion

        #region 时间段过滤参数
        [Parameter("启用时间段过滤", DefaultValue = true, Group = "时间段过滤")]
        public bool EnableSessionFilter { get; set; }

        [Parameter("亚洲时段价差倍数", DefaultValue = 1.5, MinValue = 1.0, MaxValue = 3.0, Group = "时间段过滤")]
        public double AsianSessionMultiplier { get; set; }

        [Parameter("伦敦时段价差倍数", DefaultValue = 1.0, MinValue = 0.8, MaxValue = 1.5, Group = "时间段过滤")]
        public double LondonSessionMultiplier { get; set; }

        [Parameter("纽约时段价差倍数", DefaultValue = 1.2, MinValue = 1.0, MaxValue = 2.0, Group = "时间段过滤")]
        public double NewYorkSessionMultiplier { get; set; }
        #endregion

        #region 新闻事件避让参数
        [Parameter("启用新闻避让", DefaultValue = false, Group = "新闻避让")]
        public bool EnableNewsAvoidance { get; set; }

        [Parameter("新闻前停止时间 (分钟)", DefaultValue = 30, MinValue = 5, MaxValue = 120, Group = "新闻避让")]
        public int NewsStopMinutes { get; set; }

        [Parameter("新闻后恢复时间 (分钟)", DefaultValue = 15, MinValue = 5, MaxValue = 60, Group = "新闻避让")]
        public int NewsResumeMinutes { get; set; }
        #endregion

        #region 私有字段
        private AverageTrueRange _atr;
        private MovingAverage _priceMA;
        private RelativeStrengthIndex _rsi;
        
        private double _currentSpread;
        private double _dailyPnL;
        private DateTime _lastTradeDate;
        private DateTime _lastNewsTime;
        private bool _newsAvoidanceActive;
        
        private int _buyPositionCount;
        private int _sellPositionCount;
        
        private const string LABEL = "XAUUSD_MM";
        private const double PIPS_TO_PRICE = 0.01; // XAUUSD 1 pip = 0.01
        
        // 重要新闻时间 (UTC) - 仅在特定日期生效
        private readonly Dictionary<DayOfWeek, List<TimeSpan>> _importantNewsTimes = new Dictionary<DayOfWeek, List<TimeSpan>>
        {
            // 周五非农数据 (每月第一个周五)
            [DayOfWeek.Friday] = new List<TimeSpan> { new TimeSpan(13, 30, 0) },
            // FOMC会议日 (需要手动配置具体日期)
            [DayOfWeek.Wednesday] = new List<TimeSpan> { new TimeSpan(19, 0, 0), new TimeSpan(19, 30, 0) },
            // CPI数据 (每月中旬)
            [DayOfWeek.Tuesday] = new List<TimeSpan> { new TimeSpan(13, 30, 0) }
        };
        #endregion

        #region 初始化
        protected override void OnStart()
        {
            Print($"XAUUSD高级做市机器人启动 - {DateTime.UtcNow:yyyy-MM-dd HH:mm:ss} UTC");
            
            // 验证交易品种
            if (!Symbol.Name.Contains("XAU"))
            {
                Print($"警告: 此机器人专为XAUUSD设计，当前品种: {Symbol.Name}");
            }
            
            // 初始化指标
            _atr = Indicators.AverageTrueRange(AtrPeriod, MovingAverageType.Exponential);
            _priceMA = Indicators.MovingAverage(Bars.ClosePrices, 20, MovingAverageType.Exponential);
            _rsi = Indicators.RelativeStrengthIndex(Bars.ClosePrices, 14);
            
            // 初始化变量
            _currentSpread = BaseSpreadPips;
            _dailyPnL = 0;
            _lastTradeDate = DateTime.UtcNow.Date.AddDays(-1);
            _lastNewsTime = DateTime.MinValue;
            _newsAvoidanceActive = false;
            
            _buyPositionCount = 0;
            _sellPositionCount = 0;
            
            // 订阅事件
            Positions.Opened += OnPositionOpened;
            Positions.Closed += OnPositionClosed;
            
            Print("所有指标和事件处理器初始化完成");
            Print($"基础配置: 订单量={BaseOrderSize}, 基础价差={BaseSpreadPips}点, 最大持仓={MaxPosition}盎司");
        }
        #endregion

        #region 主要交易逻辑
        protected override void OnTick()
        {
            if (!EnableStrategy)
                return;
                
            try
            {
                // 1. 更新日内统计
                UpdateDailyStats();
                
                // 2. 检查新闻事件避让
                if (EnableNewsAvoidance && CheckNewsAvoidance())
                {
                    if (!_newsAvoidanceActive)
                    {
                        Print("检测到新闻事件，暂停做市");
                        CloseAllOrders();
                        _newsAvoidanceActive = true;
                    }
                    return;
                }
                else if (_newsAvoidanceActive)
                {
                    Print("新闻事件结束，恢复做市");
                    _newsAvoidanceActive = false;
                }
                
                // 3. 检查风险限制
                if (!CheckRiskLimits())
                {
                    Print("触发风险限制，暂停交易");
                    return;
                }
                
                // 4. 计算动态价差
                CalculateDynamicSpread();
                
                // 5. 执行做市逻辑
                ExecuteMarketMaking();
            }
            catch (Exception ex)
            {
                Print($"OnTick错误: {ex.Message}");
            }
        }
        #endregion

        #region 核心功能方法
        /// <summary>
        /// 更新日内统计数据
        /// </summary>
        private void UpdateDailyStats()
        {
            var currentDate = DateTime.UtcNow.Date;
            if (_lastTradeDate != currentDate)
            {
                _dailyPnL = 0;
                _lastTradeDate = currentDate;
                Print($"新的交易日开始: {currentDate:yyyy-MM-dd}");
            }

            // 计算当日盈亏
            var todayPositions = Positions.Where(p => p.Label == LABEL && p.EntryTime.Date == currentDate);
            _dailyPnL = todayPositions.Sum(p => p.NetProfit);
        }

        /// <summary>
        /// 检查新闻事件避让
        /// </summary>
        private bool CheckNewsAvoidance()
        {
            var currentTime = DateTime.UtcNow.TimeOfDay;
            var currentDay = DateTime.UtcNow.DayOfWeek;

            // 周末不交易
            if (currentDay == DayOfWeek.Saturday || currentDay == DayOfWeek.Sunday)
                return true;

            // 检查当天是否有重要新闻
            if (_importantNewsTimes.ContainsKey(currentDay))
            {
                foreach (var newsTime in _importantNewsTimes[currentDay])
                {
                    var startAvoidTime = newsTime.Subtract(TimeSpan.FromMinutes(NewsStopMinutes));
                    var endAvoidTime = newsTime.Add(TimeSpan.FromMinutes(NewsResumeMinutes));

                    if (currentTime >= startAvoidTime && currentTime <= endAvoidTime)
                    {
                        return true;
                    }
                }
            }

            return false;
        }

        /// <summary>
        /// 检查风险限制
        /// </summary>
        private bool CheckRiskLimits()
        {
            // 检查日内风险限制
            var maxDailyLoss = Account.Balance * (MaxDailyRisk / 100);
            if (_dailyPnL < -maxDailyLoss)
            {
                Print($"触发日内最大亏损限制: {_dailyPnL:F2} < -{maxDailyLoss:F2}");
                return false;
            }

            // 检查最大持仓限制
            var currentPosition = GetNetPosition();
            if (Math.Abs(currentPosition) >= MaxPosition)
            {
                Print($"触发最大持仓限制: {Math.Abs(currentPosition):F2} >= {MaxPosition:F2}");
                return false;
            }

            return true;
        }

        /// <summary>
        /// 计算动态价差
        /// </summary>
        private void CalculateDynamicSpread()
        {
            var baseSpread = BaseSpreadPips;

            // 1. 基于ATR的波动率调整
            if (_atr.Result.LastValue > 0)
            {
                var atrInPips = _atr.Result.LastValue / Symbol.PipSize;
                if (atrInPips > VolatilityThreshold)
                {
                    baseSpread *= HighVolSpreadMultiplier;
                    Print($"高波动率检测: ATR={atrInPips:F1}点, 价差调整为: {baseSpread:F1}点");
                }
                else if (atrInPips < VolatilityThreshold * 0.5)
                {
                    baseSpread *= LowVolSpreadMultiplier;
                }
            }

            // 2. 基于时间段的调整
            if (EnableSessionFilter)
            {
                var sessionMultiplier = GetSessionMultiplier();
                baseSpread *= sessionMultiplier;
            }

            // 3. 基于库存偏斜的调整
            var inventorySkew = GetInventorySkew();
            if (Math.Abs(inventorySkew) > InventorySkewThreshold)
            {
                baseSpread *= (1 + Math.Abs(inventorySkew) / 100);
                Print($"库存偏斜调整: {inventorySkew:F1}%, 价差调整为: {baseSpread:F1}点");
            }

            // 4. 限制价差范围
            _currentSpread = Math.Max(MinSpreadPips, Math.Min(MaxSpreadPips, baseSpread));
        }

        /// <summary>
        /// 获取时间段倍数
        /// </summary>
        private double GetSessionMultiplier()
        {
            var currentHour = DateTime.UtcNow.Hour;

            // 亚洲时段 (00:00-08:00 UTC)
            if (currentHour >= 0 && currentHour < 8)
                return AsianSessionMultiplier;

            // 伦敦时段 (08:00-17:00 UTC)
            if (currentHour >= 8 && currentHour < 17)
                return LondonSessionMultiplier;

            // 纽约时段 (13:00-22:00 UTC) - 与伦敦有重叠
            if (currentHour >= 13 && currentHour < 22)
                return Math.Min(LondonSessionMultiplier, NewYorkSessionMultiplier);

            // 其他时间
            return 1.0;
        }

        /// <summary>
        /// 执行做市逻辑
        /// </summary>
        private void ExecuteMarketMaking()
        {
            var currentPrice = Symbol.Bid;
            var spreadInPrice = _currentSpread * Symbol.PipSize;

            // 计算买卖价格
            var bidPrice = currentPrice - spreadInPrice / 2;
            var askPrice = currentPrice + spreadInPrice / 2;

            // 基于库存调整订单量
            var inventorySkew = GetInventorySkew();
            var buySize = CalculateOrderSize(TradeType.Buy, inventorySkew);
            var sellSize = CalculateOrderSize(TradeType.Sell, inventorySkew);

            // 取消现有订单
            CloseAllOrders();

            // 下新订单
            if (buySize > 0 && CanPlaceOrder(TradeType.Buy))
            {
                var volumeInUnits = Symbol.QuantityToVolumeInUnits(buySize);
                PlaceLimitOrder(TradeType.Buy, Symbol.Name, volumeInUnits, bidPrice, LABEL + "_BUY");
            }

            if (sellSize > 0 && CanPlaceOrder(TradeType.Sell))
            {
                var volumeInUnits = Symbol.QuantityToVolumeInUnits(sellSize);
                PlaceLimitOrder(TradeType.Sell, Symbol.Name, volumeInUnits, askPrice, LABEL + "_SELL");
            }

            // 输出状态信息
            if (DateTime.UtcNow.Second % 30 == 0) // 每30秒输出一次
            {
                Print($"做市状态 - 价格:{currentPrice:F2}, 价差:{_currentSpread:F1}点, " +
                      $"买单:{bidPrice:F2}({buySize:F2}), 卖单:{askPrice:F2}({sellSize:F2}), " +
                      $"库存偏斜:{inventorySkew:F1}%, 日内盈亏:{_dailyPnL:F2}");
            }
        }
        #endregion

        #region 辅助方法
        /// <summary>
        /// 获取净持仓
        /// </summary>
        private double GetNetPosition()
        {
            var positions = Positions.Where(p => p.Label.StartsWith(LABEL));
            double netVolume = 0;
            foreach (var position in positions)
            {
                if (position.TradeType == TradeType.Buy)
                    netVolume += position.VolumeInUnits;
                else
                    netVolume -= position.VolumeInUnits;
            }
            return netVolume / 100000; // 转换为盎司
        }

        /// <summary>
        /// 获取库存偏斜百分比
        /// </summary>
        private double GetInventorySkew()
        {
            var netPosition = GetNetPosition();
            var maxPos = MaxPosition;

            if (maxPos == 0) return 0;

            return netPosition / maxPos * 100;
        }

        /// <summary>
        /// 计算订单量
        /// </summary>
        private double CalculateOrderSize(TradeType tradeType, double inventorySkew)
        {
            var baseSize = BaseOrderSize;

            // 基于库存偏斜调整订单量
            if (tradeType == TradeType.Buy && inventorySkew > 0)
            {
                // 已有多头持仓，减少买单量
                baseSize *= 1 - Math.Abs(inventorySkew) / 100;
            }
            else if (tradeType == TradeType.Sell && inventorySkew < 0)
            {
                // 已有空头持仓，减少卖单量
                baseSize *= 1 - Math.Abs(inventorySkew) / 100;
            }

            return Math.Max(0.01, baseSize); // 最小0.01手
        }

        /// <summary>
        /// 检查是否可以下单
        /// </summary>
        private bool CanPlaceOrder(TradeType tradeType)
        {
            var netPosition = GetNetPosition();

            if (tradeType == TradeType.Buy && netPosition >= MaxPosition)
                return false;

            if (tradeType == TradeType.Sell && netPosition <= -MaxPosition)
                return false;

            return true;
        }

        /// <summary>
        /// 关闭所有挂单
        /// </summary>
        private void CloseAllOrders()
        {
            var orders = PendingOrders.Where(o => o.Label.StartsWith(LABEL)).ToList();
            foreach (var order in orders)
            {
                CancelPendingOrder(order);
            }
        }

        /// <summary>
        /// 关闭所有持仓
        /// </summary>
        private void CloseAllPositions()
        {
            var positions = Positions.Where(p => p.Label.StartsWith(LABEL)).ToList();
            foreach (var position in positions)
            {
                ClosePosition(position);
            }
        }
        #endregion

        #region 事件处理
        /// <summary>
        /// 持仓开启事件
        /// </summary>
        private void OnPositionOpened(PositionOpenedEventArgs args)
        {
            var position = args.Position;
            if (!position.Label.StartsWith(LABEL))
                return;

            Print($"持仓开启: {position.TradeType} {position.VolumeInUnits/100000:F2}盎司 @ {position.EntryPrice:F2}");

            // 更新持仓计数
            if (position.TradeType == TradeType.Buy)
                _buyPositionCount++;
            else
                _sellPositionCount++;
        }

        /// <summary>
        /// 持仓关闭事件
        /// </summary>
        private void OnPositionClosed(PositionClosedEventArgs args)
        {
            var position = args.Position;
            if (!position.Label.StartsWith(LABEL))
                return;

            Print($"持仓关闭: {position.TradeType} {position.VolumeInUnits/100000:F2}盎司 " +
                  $"@ {position.EntryPrice:F2}, " +
                  $"盈亏: {position.NetProfit:F2}");

            // 更新持仓计数
            if (position.TradeType == TradeType.Buy)
                _buyPositionCount--;
            else
                _sellPositionCount--;

            // 更新日内盈亏
            if (position.EntryTime.Date == DateTime.UtcNow.Date)
                _dailyPnL += position.NetProfit;
        }

        /// <summary>
        /// 机器人停止事件
        /// </summary>
        protected override void OnStop()
        {
            Print("XAUUSD高级做市机器人停止");

            // 关闭所有订单和持仓
            CloseAllOrders();

            // 输出最终统计
            Print($"最终统计 - 日内盈亏: {_dailyPnL:F2}, 净持仓: {GetNetPosition():F2}盎司");

            // 取消事件订阅
            Positions.Opened -= OnPositionOpened;
            Positions.Closed -= OnPositionClosed;
        }
        #endregion

        #region 监控和报告
        /// <summary>
        /// 每小时报告
        /// </summary>
        protected override void OnBar()
        {
            if (Bars.TimeFrame != TimeFrame.Hour)
                return;

            var netPosition = GetNetPosition();
            var inventorySkew = GetInventorySkew();
            var atrInPips = _atr.Result.LastValue / Symbol.PipSize;

            Print($"小时报告 [{DateTime.UtcNow:HH:mm}] - " +
                  $"净持仓: {netPosition:F2}盎司, " +
                  $"库存偏斜: {inventorySkew:F1}%, " +
                  $"ATR: {atrInPips:F1}点, " +
                  $"当前价差: {_currentSpread:F1}点, " +
                  $"日内盈亏: {_dailyPnL:F2}");
        }
        #endregion
    }
}
