using System;
using System.Linq;
using cAlgo.API;
using cAlgo.API.Indicators;

namespace cAlgo.Robots
{
    /// <summary>
    /// XAUUSD回测专用做市机器人
    /// 简化版本，专门用于回测，确保正常开单
    /// </summary>
    [Robot(TimeZone = TimeZones.UTC, AccessRights = AccessRights.None)]
    public class XAUUSDBacktestMarketMaker : Robot
    {
        #region 参数设置
        [Parameter("启用策略", DefaultValue = true)]
        public bool EnableStrategy { get; set; }

        [Parameter("订单量 (盎司)", DefaultValue = 0.1, MinValue = 0.01, MaxValue = 10.0)]
        public double OrderSize { get; set; }

        [Parameter("价差 (点)", DefaultValue = 50, MinValue = 10, MaxValue = 200)]
        public double SpreadPips { get; set; }

        [Parameter("最大持仓 (盎司)", DefaultValue = 2.0, MinValue = 0.1, MaxValue = 20.0)]
        public double MaxPosition { get; set; }

        [Parameter("订单刷新间隔 (秒)", DefaultValue = 30, MinValue = 5, MaxValue = 300)]
        public int RefreshInterval { get; set; }
        #endregion

        #region 私有字段
        private const string LABEL = "XAUUSD_BT";
        private DateTime _lastOrderTime;
        private int _orderCount = 0;
        #endregion

        #region 初始化
        protected override void OnStart()
        {
            Print($"XAUUSD回测做市机器人启动 - {DateTime.UtcNow:yyyy-MM-dd HH:mm:ss} UTC");
            Print($"配置: 订单量={OrderSize}, 价差={SpreadPips}点, 最大持仓={MaxPosition}盎司");
            
            _lastOrderTime = DateTime.MinValue;
            
            Print("初始化完成，开始做市...");
        }
        #endregion

        #region 主要逻辑
        protected override void OnTick()
        {
            if (!EnableStrategy)
                return;

            try
            {
                // 控制下单频率
                if ((DateTime.UtcNow - _lastOrderTime).TotalSeconds < RefreshInterval)
                    return;

                // 检查基本条件
                if (Symbol.Spread / Symbol.PipSize > SpreadPips * 2)
                {
                    Print($"价差过大: {Symbol.Spread / Symbol.PipSize:F1}点，跳过此次");
                    return;
                }

                // 执行做市
                ExecuteMarketMaking();
                
                _lastOrderTime = DateTime.UtcNow;
            }
            catch (Exception ex)
            {
                Print($"OnTick错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 执行做市逻辑
        /// </summary>
        private void ExecuteMarketMaking()
        {
            // 获取当前价格
            var midPrice = (Symbol.Bid + Symbol.Ask) / 2;
            var spreadInPrice = SpreadPips * Symbol.PipSize;
            
            var bidPrice = midPrice - spreadInPrice / 2;
            var askPrice = midPrice + spreadInPrice / 2;
            
            // 检查持仓限制
            var netPosition = GetNetPosition();
            var canBuy = netPosition < MaxPosition;
            var canSell = netPosition > -MaxPosition;
            
            // 取消现有订单
            CancelAllOrders();
            
            // 下新订单
            if (canBuy)
            {
                var volumeInUnits = Symbol.QuantityToVolumeInUnits(OrderSize);
                var result = PlaceLimitOrder(TradeType.Buy, Symbol.Name, volumeInUnits, bidPrice, LABEL + "_BUY");
                if (result.IsSuccessful)
                {
                    _orderCount++;
                    Print($"买单已下: {bidPrice:F2} @ {OrderSize:F2}盎司");
                }
                else
                {
                    Print($"买单失败: {result.Error}");
                }
            }
            
            if (canSell)
            {
                var volumeInUnits = Symbol.QuantityToVolumeInUnits(OrderSize);
                var result = PlaceLimitOrder(TradeType.Sell, Symbol.Name, volumeInUnits, askPrice, LABEL + "_SELL");
                if (result.IsSuccessful)
                {
                    _orderCount++;
                    Print($"卖单已下: {askPrice:F2} @ {OrderSize:F2}盎司");
                }
                else
                {
                    Print($"卖单失败: {result.Error}");
                }
            }
            
            // 输出状态
            Print($"做市状态 - 中间价:{midPrice:F2}, 价差:{SpreadPips}点, " +
                  $"净持仓:{netPosition:F2}盎司, 总订单:{_orderCount}");
        }

        /// <summary>
        /// 获取净持仓
        /// </summary>
        private double GetNetPosition()
        {
            var positions = Positions.Where(p => p.Label.StartsWith(LABEL));
            double netVolume = 0;
            
            foreach (var position in positions)
            {
                if (position.TradeType == TradeType.Buy)
                    netVolume += position.VolumeInUnits;
                else
                    netVolume -= position.VolumeInUnits;
            }
            
            return netVolume / 100000; // 转换为盎司
        }

        /// <summary>
        /// 取消所有订单
        /// </summary>
        private void CancelAllOrders()
        {
            var orders = PendingOrders.Where(o => o.Label.StartsWith(LABEL)).ToList();
            foreach (var order in orders)
            {
                CancelPendingOrder(order);
            }
        }
        #endregion

        #region 事件处理
        protected override void OnStop()
        {
            Print("XAUUSD回测做市机器人停止");
            CancelAllOrders();
            
            var netPosition = GetNetPosition();
            var totalPositions = Positions.Count(p => p.Label.StartsWith(LABEL));
            
            Print($"最终统计:");
            Print($"- 净持仓: {netPosition:F2}盎司");
            Print($"- 总持仓数: {totalPositions}");
            Print($"- 总下单数: {_orderCount}");
        }

        protected override void OnBar()
        {
            // 每小时报告一次
            if (Bars.TimeFrame == TimeFrame.Hour)
            {
                var netPosition = GetNetPosition();
                var activeOrders = PendingOrders.Count(o => o.Label.StartsWith(LABEL));
                var totalPositions = Positions.Count(p => p.Label.StartsWith(LABEL));
                
                Print($"小时报告 [{DateTime.UtcNow:HH:mm}] - " +
                      $"净持仓:{netPosition:F2}盎司, " +
                      $"活跃订单:{activeOrders}个, " +
                      $"总持仓:{totalPositions}个, " +
                      $"累计下单:{_orderCount}次");
            }
        }

        // 订单成交事件
        protected override void OnPositionsOpened(Position position)
        {
            if (position.Label.StartsWith(LABEL))
            {
                Print($"持仓开启: {position.TradeType} {position.VolumeInUnits/100000:F2}盎司 @ {position.EntryPrice:F2}");
            }
        }

        // 持仓关闭事件
        protected override void OnPositionsClosed(Position position)
        {
            if (position.Label.StartsWith(LABEL))
            {
                Print($"持仓关闭: {position.TradeType} {position.VolumeInUnits/100000:F2}盎司, 盈亏: {position.NetProfit:F2}");
            }
        }
        #endregion
    }
}
